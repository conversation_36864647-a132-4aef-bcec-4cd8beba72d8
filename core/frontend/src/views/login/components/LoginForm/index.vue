<template>
    <a-form
        id="b-login-form"
        :form="form"
        class="login-form"
        @submit="handleSubmit"
    >
        <h2 class="center mb32" v-if="siteConfig"><img :src="siteConfig.logoUrl" alt="" width="56" height="56"></h2>
        <a-form-item>
            <a-input
                v-decorator="[
                'userName',
                { rules: [{ required: true, message: '请输入用户名!' }] }
                ]"
                placeholder="用户名"
            >
                <a-icon
                slot="prefix"
                type="user"
                style="color: rgba(0,0,0,.25)"
                />
            </a-input>
        </a-form-item>
        <a-form-item>
            <a-input
                v-decorator="[
                'password',
                { rules: [{ required: true, message: '请输入密码!' }] }
                ]"
                type="password"
                placeholder="密码"
            >
                <a-icon
                slot="prefix"
                type="lock"
                style="color: rgba(0,0,0,.25)"
                />
            </a-input>
        </a-form-item>
        <!--<a-checkbox
            v-decorator="[
            'remember',
            {
                valuePropName: 'checked',
                initialValue: true,
            }
            ]"
        >
            Remember me
        </a-checkbox> -->
        <a-form-item>
            <a-row :gutter="8">
                <a-col :span="14">
                <a-input
                    placeholder="验证码"
                    v-decorator="[
                    'captcha',
                    { rules: [{ required: true, message: '请输入验证码!', len: 4 }] },
                    ]"
                />
                </a-col>
                <a-col :span="10">
                <div
                    v-html="imageHTML"
                    class="captcha-img"
                    @click="changeCaptcha">
                </div>
                </a-col>
            </a-row>
        </a-form-item>
        <a-form-item>
        <a-button
            type="primary"
            html-type="submit"
            :loading="confirmLoading"
            class="login-form-button"
        >
            登录
        </a-button>
        <!-- <a-button
            type="normal"
            class="login-form-button"
            @click="loginByWeibo"
        >
            微博登录
        </a-button> -->
        </a-form-item>
    </a-form>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import utils from 'utils/utils'
import { encryptedData } from 'utils/encrypt'
import { shortTitle } from 'config/settings'

export default {
    data() {
        return {
            shortTitle,
            visible: true,
            confirmLoading: false,
            imageHTML: '',
            loginId: '',
            tourl: this.$route.query.tourl
        }
    },
    components: {},
    props: {},
    computed: {
        ...mapState('site', ['siteConfig']),
    },
    created() {},
    mounted() {
        this.changeCaptcha()
    },
    beforeCreate () {
        this.form = this.$form.createForm(this);
    },
    methods: {
        changeCaptcha(){
            this.$api.user.getSvgcaptcha().then(res => {
                this.imageHTML = res.img
                this.loginId = res.loginId
            })
        },
        handleSubmit(e) {
            e.preventDefault();
            this.form.validateFields(async (err, values) => {
                if (!err) {
                    this.confirmLoading = true;
                    
                    const password = await encryptedData(values.password);

                    this.$store.dispatch('user/login', {
                        username: values.userName,
                        password: password,
                        captcha: values.captcha,
                        loginId: this.loginId
                    }).then((result) => {
                        this.confirmLoading = false;
                        this.toLogin(this.tourl, this.$route.query.redirect, result)
                    }).catch(error => {
                        this.confirmLoading = false;
                        this.$router.push({
                            name: 'login'
                        });
                        console.log(error);
                    });
                }
            })
        },
    },
}
</script>

<style scoped lang="less">
#b-login-form {
    width: 100%;
    // height: 300px;

    .captcha-img {
        width: 100;
        height: 40px;
    }
}

#b-login-form .login-form-button {
  width: 100%;
}
</style>