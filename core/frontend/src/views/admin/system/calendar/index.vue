<template>
    <div class="b-calendar">
        <!-- <a-alert class="mb16" message="全量展示，注意题材数量控制" banner /> -->
        <a-card size="small" class="mb32">
            <a-alert class="mb16" message="投资日历：美联储FED、重要时间点、公司公告、国内外重要会议等重要财经事件。" banner />
            <a-switch v-model="showStatus" v-role="['admin.top']" checked-children="量化状态" un-checked-children="关闭"></a-switch>
            <a-calendar @change="onDateChange" @panelChange="onDateChange">
                <div 
                    class="b-calendar-cell" 
                    :class="{'b-calendar-cell-active': getCalendar(value).mark, 'b-calendar-cell-danger': getCalendar(value).danger}"
                    slot="dateFullCellRender" 
                    slot-scope="value">
                    <div class="ant-fullcalendar-date" @contextmenu.prevent.stop="showContextmenu($event, 'calendar', value, fetchData)">
                        <div class="ant-fullcalendar-value">
                            <span class="xiu fl" v-if="!(getDaliyData(value) && getDaliyData(value).isOpen)">休</span>
                            {{value.get('date')}}
                        </div>
                        <div class="ant-fullcalendar-content">
                            <ul class="events">
                                <li v-for="(item, index) in getListData(value)" :key="index" @click="showDetail(value, item.event, item.calendarId)">
                                    <div v-if="item.event">
                                        <a-badge class="b-calendar-badge" :status="item.event.type" :text="item.event.content" />
                                    </div>
                                </li>
                            </ul>
                            <div class="tasks fs12" v-if="getDaliyData(value) && getDaliyData(value).isOpen && showStatus" v-role="['admin.top']">
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).sentiment ? 'success' : 'error'" title="评分" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).volume ? 'success' : 'error'" title="指数" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).crowding ? 'success' : 'error'" title="量化" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).SMB ? 'success' : 'error'" title="多因子" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).totalvalue ? 'success' : 'error'" title="总市值" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).rzye ? 'success' : 'error'" title="两融" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).style ? 'success' : 'error'" title="风格" />
                                <!-- <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).sentiment ? 'success' : 'error'" text="评分" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).volume ? 'success' : 'error'" text="指数" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).crowding ? 'success' : 'error'" text="量化" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).totalvalue ? 'success' : 'error'" text="总市值" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).NorthMoney ? 'success' : 'error'" text="北向" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).rzye ? 'success' : 'error'" text="两融" />
                                <a-badge class="b-calendar-task-badge" :status="getDaliyData(value).starcount ? 'success' : 'error'" text="趋势" /> -->
                            </div>
                        </div>
                    </div>
                </div>
                <template slot="monthCellRender" slot-scope="value">
                    <li v-for="(item, index) in getMonthData(value)" :key="index">
                        <span class="fs12">{{item}}</span>
                    </li>
                </template>
            </a-calendar>
            <span v-role="['admin.top']">量化状态：评分、指数、量化、多因子、总市值、两融、风格</span>
        </a-card>
        <a-card size="small">
            <a-alert class="mb16" message="一些重要的时间点。" banner />
            <h3>第一，宏观月度常规操作：包括宏观经济数据披露，以及央行每月的MLF、LPR操作。</h3>

            <p>每月的宏观经济数据是观察国内经济运行状况的重要窗口，对A股也会有较大的影响。
            比如PMI、社融对国内经济有一定的前瞻指示作用，PMI、社融超预期可能会提振市场情绪，而CPI、PPI可以观察物价的走势，尤其是PPI对周期板块有较强的指示意义。

            <strong>从披露时间来看，PMI是当月最后一天披露，而CPI、PPI、进出口、社融、社零、固定投资等数据要到第二个月7-15号左右披露。</strong>

            除了经济数据披露之外，<strong>央行一般会在每个月15号左右操作MLF，每个月20号左右操作LPR，</strong></p>

            <h3>第二，重要的宏观会议：两会+政治局会议+经济工作会议。</h3>

            <p>每年3月全国两会，主要看政府工作报告。

            <strong>每年4、7、10、12月的政治局会议</strong>，会对每个季度的国内经济做一个总结，并对下一季度做展望规划，随着经济形势的变化对宏观政策进行调整（其中4、7、12是固定的，10月份的不一定有）。

            <strong>每年年底的经济工作会议，是国内经济工作最高规格的会议</strong>，主要讨论部署第二年的经济工作，指示意义非常大。</p>

            <h3>第三，A股业绩时间线：财报密集披露期会对市场产生较大的影响。</h3>
            
            <ul>
                <li>【1月31日】所有板块年报预告（有条件强制）；</li>
                <li>【2月28日】科创板业绩快报或正式年报（强制）；</li>
                <li>【4月15日】深证主板、中小板一季报预告（有条件强制）；</li>
                <li>【4月30日】所有板块年报和一季报（强制）；</li>
                <li>【5月-6月】业绩真空期；</li>
                <li>【7月15日】深证主板、中小板中报预告（有条件强制）；</li>
                <li>【8月31日】所有板块中报（强制）；</li>
                <li>【10月15日】深证主板、中小板三季报预告（有条件强制）；</li>
                <li>【10月31日】所有板块三季报（强制）；</li>
                <li>【11月-12月】业绩真空期。</li>
            </ul>

            <p>上述日期均为相关财报披露截止日期，而在截止日期前1-2周，会迎来该阶段财报的密集披露期，财报密集披露期会对市场产生较大的影响。</p>

            <h3>第四，行业重要事件：比如产业大事件，以及某些行业每个月披露的产业数据。</h3>

            <p>产业事件比如每年1月份的国际消费电子展，可以说是消费电子行业的盛宴，会展示当前最尖端的产业技术，比如这两年的折叠屏、5G手机等等，对科技板块影响比较大。

            再比如每年三季度的苹果、华为的发布会，也会影响相关产业链的预期。

            喜欢电影板块的粉丝则要关注每年贺岁档、春节档、暑期档、国庆档等黄金档期。

            而月度的产业高频数据则更多，比如：</p>
            <ul>
                <li>汽车行业产销数据会在每个月12-15号左右公布；</li>
                <li>航空公司经营数据会在每个月14-15号左右公布；</li>
                <li>面板价格数据会在每个月上旬、下旬各公布一次。</li>
            </ul>
        </a-card>
        <AddCalendar ref="calendar" :event="calendarData" />
    </div>
</template>

<script>
import moment from 'moment';
import AddCalendar from 'components/AddCalendar'

export default {
    data() {
        return {
            visible: false,
            calendars: [],
            daliyData: null,
            currentTime: moment(),
            event: {
                calendarId: 0,
                eventId: 0,
                currentTime: moment(),
                type: 'success',
                frequency: 'day',
                content: '',
            },
            calendarData: null,
            showStatus: false
        }
    },
    props: {},
    components: {
        AddCalendar
    },
    computed: {},
    created() {},
    mounted() {
        this.fetchData();
        this.$eventBus.$on('calendar-event', (val) => {
            this.fetchData();
        });
    },
    methods: {
        handleChange(value) {
            console.log(`selected ${value}`);
        },
        fetchData() {
            this.$api.calendar.calendarList({
                type: 'all',
                date: this.currentTime.format('YYYYMMDD'),
            }).then((result) => {
                this.calendars = result.calendarData;
                this.daliyData = result.daliyData;
            }).catch((err) => {

            });
        },
        showDetail(value, item, calendarId) {
            if (!this.$role('admin.top')) {
                return;
            }
            this.$refs['calendar'].show()
            this.calendarData = {
                eventId: item.id,
                calendarId: calendarId,
                frequency: item.frequency,
                content: item.content,
                type: item.type,
                currentTime: value,
                related: item.related,
                category: item.category,
                description: item.description,
            };
        },
        onDateChange(value) {
            this.currentTime = value;
            this.fetchData();
        },
        getListData(value) {
            let listData = [];
            
            this.calendars.forEach(element => {
                if (element.date === value.format('YYYYMMDD')) {
                    listData = element.calendarEvents;
                }
            });
            return listData;
        },
        getDaliyData(value) {
			let daliy = null;
			
			this.daliyData && this.daliyData.forEach(element => {
				if (element.date === value.format('YYYYMMDD')) {
					daliy = element;
				}
			});
			return daliy;
		},
        getCalendar(value) {
            let obj = {
                mark: false,
                danger: false,
            }
            this.calendars.forEach(element => {
                if (element.date === value.format('YYYYMMDD')) {
                    obj = element;
                }
            });

            return obj
        },
        getMonthData(value) {
            const index = moment(value).month();
            // 修复未定义的EVENT对象问题
            const list = []; // 或者使用适当的默认数据

            return list[index] || [];
        }
    }
}
</script>

<style scoped lang="less">
.b-calendar-cell {
    width: 100%;
    height: 100%;
}
.events {
  list-style: none;
  margin: 0;
  padding: 0;
}
.events .ant-badge-status {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  text-overflow: ellipsis;
  font-size: 12px;
}

.smallcube {
    width: 10px;
    height: 10px;
    background: #f2f3f5;
    display: none;
    margin-top: 6px;
}

.show-red {
    display: inline-block;
    background: #f5222d;
}

.show-green {
    display: inline-block;
    background: #52c41a;
}

.show {
    display: inline-block;
}
.b-calendar-cell-content {
    display: flex;
	align-items: center;
    justify-content: center;
}

.xiu {
	display: block;
	height: 12px;
	width: 12px;
	line-height: 12px;
	color: #fff;
	font-size: 10px;
	text-align: center;
	background: #faad14;
	margin: 0 2px;
    margin-top: 6px;
}
</style>