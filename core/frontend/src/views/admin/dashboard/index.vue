<template>
    <div>
        <div class="b-home-empty"  v-if="!result">
            <a-spin size="large" />
        </div>
        <div v-else class="b-home">
            <a-alert class="mb16" :message="siteConfig.sideText" v-if="siteConfig && siteConfig.sideText" banner :style="{'width': '45%'}" />
            <div class="b-home-side" v-if="siteConfig && siteConfig.sideUrl">
                <a :href="siteConfig.sideUrl" target="_blank">
                    <img width="100%" :src="siteConfig.sideImgUrl" alt="" />
                </a>
            </div>
            <a-tabs type="card" size="small">
                <a-tab-pane key="1" tab="行情" forceRender>
                    <a-alert class="mb32" :message="result.saying.title" type="info" closeText="关闭" v-if="result && result.saying" />
                    <a-row :gutter="16">
                        <a-col :span="24">
                            <a-card class="mb32" v-if="result.daliy" size="small">
                                <a-row :gutter="60">
                                    <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="10" class="mb16">
                                        <a-statistic title="市场情绪" :value="(result.daliy.sentiment * 100).toFixed(2)">
                                            <template #suffix><TaskCheck :task='29' /></template>
                                        </a-statistic>
                                        <a-row :gutter="16">
                                            <a-col :span="6" v-for="(arr, k) in [(result.ranking || []).slice(0, 16), (result.ranking || []).slice(16, 32), (result.ranking || []).slice(32, 48), (result.ranking || []).slice(48, 64)]" :key="k">
                                                <div class="b-home-ranking-item" :class="{'b-home-ranking-item-red': tag.ranking - tag.lastranking > 20}" v-for="(tag, key) in arr" :key="key" @dblclick.stop="addStopword(tag.text)">
                                                    <span class="num fl">{{key+k*8+1}}</span><span class="text">{{tag.text}}</span>
                                                    <span class="fr" v-if="tag.ranking != tag.lastranking"><a-icon :type="tag.ranking - tag.lastranking > 0 ? 'arrow-up' : 'arrow-down'" :style="{ fontSize: '12px', color: tag.ranking - tag.lastranking > 0 ? '#DD2200' : '#009933' }" /><span class="fs12">{{Math.abs(tag.ranking - tag.lastranking)}}</span></span>
                                                </div>
                                            </a-col>
                                        </a-row>
                                    </a-col>
                                    <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="14">
                                        <Indicators ref="subChart12" :key="componentKey + 12" />
                                    </a-col>
                                </a-row>
                            </a-card>
                        </a-col>
                        <a-col :span="24">
                            <div class="mb32">
                                <Calendar ref="subChart1" :key="componentKey + 1" />
                            </div>
                        </a-col>
                        <a-col :span="4">
                            <div class="mb32 section" v-if="result && result.tags">
                                <a-card title="热门科技题材" :bordered=true size="small">
                                    <div v-if="result.tags && result.tags.length > 0">
                                        <div v-for="(item, index) in result.tags" :key="index">
                                            <router-link :to="{path: '/admin/market/tag', query: {tag: item.tag.name}}" v-if="item.tag">
                                                {{item.tag.name}} <span class="center" v-html="replaceMethod(item.avg, item.avg, 0, 2, '%')" />   
                                            </router-link>
                                        </div>
                                    </div>
                                    <a-empty v-else />
                                    <span slot="extra"><router-link to="/admin/market/tag">看题材</router-link></span>
                                </a-card>
                            </div>
                        </a-col>
                        <!-- <a-col :span="16">
                            <div class="mb32 section">
                                <ChartBeixiang  ref="subChart4" :key="componentKey + 4"/>
                            </div>
                        </a-col> -->
                        <a-col :span="12">
                            <div class="mb32 section">
                                <ChartZhangdie ref="subChart3" :key="componentKey + 3" />
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div class="mb32 section">
                                <ChartZhangdieByValue ref="subChart7" :key="componentKey + 7" />
                            </div>
                        </a-col>
                        <a-col :span="24">
                            <div class="mb32">
                                <ChartPlateTreemap ref="subChart2" :key="componentKey + 2" />
                            </div>
                        </a-col>
                    </a-row>
                </a-tab-pane>
                <a-tab-pane key="2" tab="热门" forceRender>
                    <a-row :gutter="16">
                        <a-col :span="24">
                            <div class="mb32">
                            <StockTrend ref="subChart9" :key="componentKey + 9" />
                            </div>
                        </a-col>
                        <a-col :span="24">
                            <div class="mb32">
                            <a-tabs default-active-key="4">
                                <a-tab-pane key="1" tab="机构研报">
                                    <a-card :bordered=true size="small">
                                        <div class="mb16">
                                            <TaskCheck :task='21' name="机构研报(近六个月) " />
                                        </div>
                                        <a-row :gutter="12">
                                            <div v-if="result && result.stockReport">
                                                <a-col :span="6" v-for="(arr, k) in [(result.stockReport || []).slice(0, 25), (result.stockReport || []).slice(25, 50), (result.stockReport || []).slice(50, 75), (result.stockReport || []).slice(75, 100)]" :key="k">
                                                    <div v-for="(stock, key) in arr" :key="key">
                                                        <StockPopover class="mb12" :source="stock.source" :stock="stock" :extra="true" :tag="false">
                                                            <template slot="end">
                                                                ({{stock.recentReportCount}})
                                                            </template>
                                                        </StockPopover> 
                                                    </div>
                                                </a-col>
                                            </div>
                                        </a-row>
                                    </a-card>
                                </a-tab-pane>
                                <a-tab-pane key="2" tab="机构主力">
                                    <a-card :bordered=true size="small">
                                        <div class="mb16">
                                            <TaskCheck :task='13' name="机构龙虎榜" />
                                        </div>
                                        <a-row :gutter="12">
                                            <div v-if="result && result.stockOrg">
                                                <a-col :xs="12" :sm="12" :md="12" :lg="12" :xl="6" v-for="(stock, key) in result.stockOrg" :key="key">
                                                    <StockPopover class="mb12" :source="stock.source" :stock="stock.stock">
                                                        <template slot="end">
                                                            <span class="fr" v-html="replaceMethod(stock.pBuy, stock.pBuy, 8, 2, '亿')" />
                                                        </template>
                                                    </StockPopover>
                                                </a-col>
                                            </div>
                                        </a-row>
                                    </a-card>
                                </a-tab-pane>
                                <a-tab-pane key="3" tab="资金流向">
                                    <a-card :bordered=true size="small">
                                        <a-row :gutter="12">
                                            <a-col :span="12">
                                                <div class="b-home-stock-head mb16">主力资金</div>
                                            </a-col>
                                            <a-col :span="12">
                                                <div class="b-home-stock-head mb16">北上买卖占比 <TaskCheck :task='12' /></div>
                                            </a-col>
                                            <div v-if="result && result.stockFunds">
                                                <a-col :xs="12" :sm="12" :md="12" :lg="12" :xl="6" v-for="(arr, k) in [result.stockFunds.netBuy, result.stockFunds.netSale]" :key="k">
                                                    <div v-for="(stock, key) in arr" :key="key">
                                                        <StockPopover class="mb12" :source="stock" :stock="stock.stock" :extra="true">
                                                            <template slot="end">
                                                                (<span v-html="replaceMethod(stock.f62, stock.f62, 8, 2, '亿')" />)  
                                                            </template>
                                                        </StockPopover>
                                                    </div>
                                                </a-col>
                                                <a-col :xs="12" :sm="12" :md="12" :lg="12" :xl="6" v-for="(arr, k) in [result.stockFunds.northBuyPer, result.stockFunds.northSalePer]" :key="k">
                                                    <div v-for="(stock, key) in arr" :key="key">
                                                        <StockPopover class="mb12" :source="stock" :stock="stock.stock">
                                                            <template slot="end">
                                                                <span class="fr" v-html="replaceMethod(stock.sharesz_chg_one, stock.sharesz_chg_one, 4, 2, '亿')" />
                                                                (<span v-html="replaceMethod(stock.zzb_one, stock.zzb_one, 0, 2, '‰')" />)
                                                            </template>
                                                        </StockPopover>
                                                    </div>
                                                </a-col>
                                            </div>
                                        </a-row>
                                    </a-card>
                                </a-tab-pane>
                                <a-tab-pane key="4" tab="阶段收益率">
                                    <a-alert class="mb16" message="量化配置阶段时间+筛选条件的个股收益率排行。" banner />
                                    <a-card title="阶段收益率" :bordered=true size="small">
                                        <div slot="extra">
                                            <TaskCheck :task='27' name="个股模型计算" />
                                        </div>
                                        <a-row :gutter="16">
                                            <a-col :xs="12" :sm="12" :md="12" :lg="12" :xl="6" v-for="(arr, key) in [(result.decisionRent || []).slice(0, 25), 
                                            (result.decisionRent || []).slice(25, 50), (result.decisionRent || []).slice(50, 75), 
                                            (result.decisionRent || []).slice(75, 100)]" :key="key">
                                                <div v-for="(stock, key2) in arr" :key="key2">
                                                    <StockPopover :source="stock.source" :stock="stock">
                                                        <template slot="end">
                                                            <span class="fr" v-html="replaceMethod(stock.decisionPercent, stock.decisionPercent * 100, 0, 2, '%')" /> 
                                                        </template>
                                                    </StockPopover>   
                                                </div>
                                            </a-col>
                                        </a-row>
                                    </a-card>
                                </a-tab-pane>
                            </a-tabs>
                            </div>
                        </a-col>
                    </a-row>
                </a-tab-pane>
                <a-tab-pane key="3" tab="趋势" forceRender>
                    <a-row :gutter="16">
                        <a-col :span="24">
                            <div class="mb32">
                            <StockStar ref="subChart8" :key="componentKey + 8" />
                            </div>
                        </a-col>
                    </a-row>
                </a-tab-pane>
                <a-tab-pane key="4" tab="组合" forceRender>
                    <a-row :gutter="16">
                        <a-col :span="24" v-role="['admin.top', 'supervip', 'partner']">
                            <a-alert class="mb16" message="【VIP】市场主线跟踪，日常舆情跟踪。" banner />
                            <div class="mb32">
                            <StockGroup ref="subChart10" :key="componentKey + 10" />
                            </div>
                        </a-col>
                    </a-row>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
</template>

<script>
// import ChartBeixiang from 'components/chart/ChartBeixiang-shishi.vue'
import ChartAmount from 'components/chart/ChartAmount.vue'
import ChartZhangdie from 'components/chart/ChartZhangdie'
// import Plate from 'components/Plate'
import ChartPlateTreemap from 'components/chart/ChartPlateTreemap'
import Calendar from 'components/Calendar'
import ChartRzrq from 'components/chart/ChartRzrq'
import StockPopover from 'components/StockPopover'
import StockStar from 'components/StockStar'
import StockTrend from 'components/StockTrend'
import StockGroup from "components/StockGroup";
import ChartWorths from 'components/chart/ChartWorths'
import ChartZhangdieByValue from 'components/chart/ChartZhangdieByValue'
import TaskCheck from 'components/TaskCheck'
import Indicators from 'components/Indicators'
import { mapActions, mapState } from 'vuex'
import EVENT from 'constants/event.js'

export default {
    data() {
        return {
            result: null,
            timer: null,
            componentKey: 100,
            screenWidth: document.body.clientWidth,
            stocktag: EVENT.STOCK_TAG,
            industry: ''
        }
    },
    components: {
        ChartZhangdie,
        ChartWorths,
        ChartAmount,
        ChartRzrq,
        StockPopover,
        ChartPlateTreemap,
        Calendar,
        TaskCheck,
        ChartZhangdieByValue,
        StockStar,
        StockTrend,
        StockGroup,
        Indicators
    },
    props: {},
    computed:{
        ...mapState('site', ['siteConfig']),
    },
    created() {},
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    },
    mounted() {
        this.$store.commit('stock/hideStock');
        this.stopBodyScroll(false);
        this.getHomeData();
        this.timer = setInterval(() => {
            this.getHomeData();
            for (let index = 1; index < 13; index++) {
                this.$refs['subChart' + index] && this.$refs['subChart' + index].fetchData()
            }
            // this.forceRerender();
        }, 1000 * 10);
    },
    methods: {
        ...mapActions(('stock'),['showStockDetail']),
        changeIndustry(value) {
            this.industry = value
        },
        forceRerender() {
			this.componentKey += 1;
		},
        getHomeData() {
            this.$store.commit('site/showLoading')
            this.$api.black.home({
                key: 'saying,daliy,sortRank,tags,ranking,stockReport,stockOrg,stockFunds,decisionRent'
            }).then((result) => {
                this.result = result;
            }).catch((err) => {

            }).finally(() => {
                this.$store.commit('site/hideLoading')
            })
        },
    }
    
}
</script>

<style scoped lang="less">
.b-home-empty {
    text-align: center;
    padding-top: 80px;
}
.b-home-news {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
}

.b-home-side {
    position: absolute;
    right: 24px;
    top: 0;
    width: 180px;
    height: 80px;
    z-index: 99;
    overflow: hidden;
    background: #fff;
}

.b-home-stock-item {
    border: 1px solid #efefef;
    padding: 8px 6px;
    margin-bottom: 8px;
    height: 40px;
}

.b-home-section-green {
    background-color: rgb(196 243 177);
}
.b-home-section-red {
    background-color: rgb(245 142 91);
}
.stock-sign-red {
    font-size: 10px;
    // border: 1px solid rgb(151, 4, 48);
    background: rgb(228, 14, 78);
    color: #fff;
    font-weight: bold;
    height: 14px;
    line-height: 14px;
    width: 16px;
    display: inline-block;
    text-align: center;
    margin-right: 3px;
    margin-top: -1px;
}

.b-home-ranking-item {
    overflow: hidden;
    height: 26px;
    line-height: 26px;
    .num {
        font-size: 12px;
        display: inline-block;
    }
    .text {
        // word-break: break-all;
        display: inline-block;
        white-space: pre-line;
        max-width: 80px;
        overflow: hidden;
        margin-left: 4px;
    }
}

.b-home-ranking-item-red {
    background: rgba(245, 34, 45, 0.3);

    border-radius: 4px;
}

</style>