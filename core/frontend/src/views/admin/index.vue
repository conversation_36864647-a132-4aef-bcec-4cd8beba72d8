<template>
    <div>
        <!-- 登录后 -->
        <a-layout id="app">
            <a-layout-sider
                :trigger="null"
                collapsible
                v-model="collapsed"
                :style="{ overflow: 'auto', height: '100vh', position: 'fixed', left: 0 }"
                id="sidebar">
                <div class="logo" v-if="siteConfig">
                    <img class="logo-img" v-if="siteConfig" :src="siteConfig.logoUrl" alt="" width="28" height="28">
                    <span v-if="!collapsed" class="logo-title">{{siteConfig.siteTitle}}</span>
                </div>
                <a-menu 
                    theme="dark" 
                    mode="inline" 
                    :defaultSelectedKeys="defaultSelectedKeys" 
                    :selectedKeys="selectedKeys"
                    @click="menuClick">
                    <template v-for="m in menus">
                        <a-sub-menu v-if="m.children && !m.hidden" :key="m.code">
                            <span slot="title"><a-icon :type="m.icon" /><span>{{m.title}}</span></span>
                            <template v-for="sub in m.children">
                                <a-menu-item v-if="!sub.hidden" :key="sub.code">{{sub.title}}</a-menu-item>
                            </template>
                        </a-sub-menu>
                        <a-menu-item v-if="!m.children && !m.hidden" :key="m.code">
                            <a-icon :type="m.icon" />
                            <span class="nav-text">{{m.title}}</span>
                        </a-menu-item>
                    </template>
                    <a-menu-item v-if="$role('admin.top')" key="admin-search">
                        <a-icon type="search" />
                        <span class="nav-text">搜索</span>
                    </a-menu-item>
                    <a-menu-item v-if="$role('admin.top')" key="app-dashboard">
                        <a-icon type="mobile" />
                        <span class="nav-text">手机版</span>
                    </a-menu-item>
                </a-menu>
                <div class="menu-operate">
                    <a-icon
                        class="trigger"
                        :type="collapsed ? 'menu-unfold' : 'menu-fold'"
                        @click="()=> collapsed = !collapsed"
                    />
                </div>
                <!-- <span class="menu-version fs12">{{version}}</span> -->
            </a-layout-sider>
            <a-layout :style="{ marginLeft: this.collapsed ? '80px' : '200px', backgroud: '#fff' }">
                <!-- <a-layout-header id="header">
                    <a-menu 
                        theme="light" 
                        mode="horizontal" 
                        :defaultSelectedKeys="defaultSelectedKeys" 
                        :selectedKeys="selectedKeys"
                        @click="menuClick">
                            <a-menu-item  v-for="m in tabs" :key="m.code">
                                <span v-if="m.title=='新闻'"><span class="nav-text">{{m.title}}</span><a-badge :number-style="{ marginTop: '-4px' }" :count="newsCount" :overflow-count="99"></a-badge></span>
                                <span v-else-if="m.title=='阅读'"><span class="nav-text">{{m.title}}</span><a-badge :number-style="{ marginTop: '-4px' }" :count="mediaCount" :overflow-count="99"></a-badge></span>
                                <span v-else-if="m.title=='研报'"><span class="nav-text">{{m.title}}</span><a-badge :number-style="{ marginTop: '-4px' }" :count="researchsCount" :overflow-count="99"></a-badge></span>
                                <span v-else class="nav-text">{{m.title}}</span>
                            </a-menu-item>
                    </a-menu>
                    <div class="menu-extra">
                        <span class="fr fs16">{{userInfo && userInfo.username}}</span>
                        <a-dropdown>
                            <a-avatar :src="userInfo && userInfo.photo" :size="30" icon="user" class="fr user mr4" :style="{'marginTop': '15px'}" />
                            <a-menu slot="overlay">
                                <a-menu-item v-role="['admin.top']" @click="addNewPost"> 
                                    <a-icon type="form" /><span>写记录</span>
                                </a-menu-item>
                                <a-menu-item> 
                                    <router-link :to="{name: 'admin-userinfo'}"><a-icon type="user" class="mr4" /> <span>个人中心</span></router-link>
                                </a-menu-item>
                                <a-menu-item @click="logout"> 
                                    <a-icon type="logout" :style="{'color':'#de2b2b'}" /><span>退出登录</span>
                                </a-menu-item>
                            </a-menu>
                        </a-dropdown>
                        <MessageCenter class="fr mr16" />
                        <div class="fr">
                            <a-icon v-if="loading" type="loading" :style="{'fontSize':'28px','color':'#1890ff','marginRight':'24px','marginTop':'4px'}" />
                        </div>
                    </div>
                </a-layout-header> -->
                <a-layout-content id="content">
                    <!-- <resize-observer @notify="handleResize"></resize-observer> -->
                    <router-tab :tabs="tabs" :max-alive="3">
                        <template #start>
                        </template>
                        <template #default="tab">
                            <a-icon class="mr4" v-if="tab.title=='首页'" type="home" />
                            <span class="router-tab__item-title" :title="tab.tips">{{tab.title}}</span>
                            <a-badge v-if="tab.title=='新闻'" :count="newsCount" :overflow-count="99"></a-badge>
                            <a-badge v-if="tab.title=='阅读'" :count="mediaCount" :overflow-count="99"></a-badge>
                            <a-badge v-if="tab.title=='研报'" :count="researchsCount" :overflow-count="99"></a-badge>
                            <i
                                v-if="tab.closable"
                                class="router-tab__item-close"
                                @click.prevent="tab.close"
                            />
                        </template>
                        <template #end>
                            <span class="fr fs16" :class="{'gold': $role('supervip')}">{{userInfo && userInfo.username || userInfo.nickname}}</span>
                            <a-dropdown>
                                <a-avatar :src="userInfo && userInfo.photo" :size="30" icon="user" class="fr user mr8" />
                                <a-menu slot="overlay">
                                    <a-menu-item v-role="['admin.top']" @click="addNewPost"> 
                                        <a-icon type="form" /><span>写记录</span>
                                    </a-menu-item>
                                    <a-menu-item> 
                                        <router-link :to="{name: 'admin-userinfo'}"><a-icon type="user" class="mr4" /> <span>个人中心</span></router-link>
                                    </a-menu-item>
                                    <a-menu-item @click="logout"> 
                                        <a-icon type="logout" :style="{'color':'#de2b2b'}" /><span>退出登录</span>
                                    </a-menu-item>
                                </a-menu>
                            </a-dropdown>
                            <MessageCenter class="fr mr16" />
                            <a-divider class="fr mt16" type="vertical" />
                            <a-dropdown v-if="siteConfig">
                                <span class="red fr"><a-icon type="heart" /> 微信赞助</span>
                                <img slot="overlay" :src="siteConfig.wxImgUrl" width="220" height="220" class="" />
                            </a-dropdown>
                        </template>
                    </router-tab>
                    <!-- <div> -->
                        <!-- <keep-alive>
                            <router-view :key="$route.fullpath" v-if="$route.meta.keepalive"></router-view>
                        </keep-alive>
                        <router-view :key="$route.fullpath" v-if="!$route.meta.keepalive"></router-view> -->
                        <resize-observer @notify="handleResize"></resize-observer>
                    <!-- </div> -->
                </a-layout-content>
                <a-layout-footer id="footer" class="fs12">
                    <!-- 秋实系统 -->
                    <router-link :to="{path: '/admin/about', query: {tab: 1}}" class="no-link">版权声明</router-link>
                    <a-divider type="vertical" />
                    <router-link :to="{path: '/admin/about', query: {tab: 2}}" class="no-link">服务协议</router-link>
                    <a-divider type="vertical" />
                    <router-link :to="{path: '/admin/about', query: {tab: 3}}" class="no-link">免责声明</router-link>
                    <a-divider type="vertical" />
                    <router-link :to="{path: '/admin/about', query: {tab: 4}}" class="no-link">关于本站</router-link>
                    <a-divider type="vertical" />
                    {{version}}
                </a-layout-footer>
                <div id="loading">
                    <a-icon v-if="loading" type="loading" :style="{'fontSize':'28px','color':'#1890ff','marginRight':'24px','marginTop':'4px'}" />
                </div>
            </a-layout>
        </a-layout>
        <div id="stock">
            <a-icon class="close" type="close-circle" @click="onClose" />
            <StockDetail 
                v-if="stockSideStatus" 
                :code="code" 
                :style="{'width': width + 'px'}">
            </StockDetail>
        </div>
        <a-modal
            v-model="visible"
            title="发状态"
            width="48%"
        >
            <Dailystory />
        </a-modal>
    </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import RefreshData from 'components/RefreshData'
import StockDetail from 'components/StockDetail'
import Dailystory from 'components/Dailystory';
import MessageCenter from 'components/MessageCenter';
import Storage from "utils/storage";
import { shortTitle, version } from 'config/settings'

export default {
    name: 'app',
    components: {
        RefreshData,
        StockDetail,
        Dailystory,
        MessageCenter
    },
    data() {
        return {
            shortTitle,
            version,
            width: window.bWidth * 0.35 - 20,
            screenWidth: Storage.getItem('screenWidth') || 0,
            height: window.bHeight,
            collapsed: true,
            defaultSelectedKeys: ['index'],
            selectedKeys: [],
            newsCount: 0,
            researchsCount: 0,
            mediaCount: 0,
            visible: false,
            timer: null,
            selectStr: '',
        }
    },
    computed:{
        ...mapState('site',['loading', 'network', 'siteConfig']),
        ...mapState('stock', ['code']),
        ...mapGetters('user', ['userInfo', 'menus', 'tabs']),
        ...mapGetters('stock', ['stockSideStatus']),
    },
    watch: {
        '$route' (to) {
            this.selectedKeys = [to.name];
        },
        network: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.openNewworkNotification();
                }
            }
        },
        stockSideStatus: {
            immediate: true,
            handler(val) {
                const d = document.getElementsByClassName("router-tab__container")
                if (!d || !d[0] || !d[0].style) {
                    return;   
                }

                if (val) {
                    document.getElementsByClassName("router-tab__container")[0].style.width = `${window.bWidth - this.width - 100}px`;
                } else {
                    document.getElementsByClassName("router-tab__container")[0].style.width = `${window.bWidth - 80}px`;
                }
            }
        },
        selectStr: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.$fetchCenter.onSearch('stock', {
                        text: val,
                        pageSize: 10,
                        pageIndex: 1
                    }, (result) => {
                        if (result && result.rows && result.rows.length > 0 && result.rows[0].code) {
                            if (val.includes(result.rows[0].displayName)) {
                                this.showStockDetail(result.rows[0].code)
                            }
                        }
                    })
                }
            }
        },
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        window.removeEventListener('online',  this.updateOnlineStatus);
        window.removeEventListener('offline', this.updateOnlineStatus);
        window.removeEventListener('scroll', this.scrollFunc);  
        // window.removeEventListener('resize', this.handleResize)
    },
    created() {
        // 在页面加载时读取sessionStorage里的状态信息
        // if (sessionStorage.getItem("store") ) {
        //     this.$store.replaceState(Object.assign({}, this.$store.state,JSON.parse(sessionStorage.getItem("store"))))
        // } 

        // //在页面刷新时将vuex里的信息保存到sessionStorage里
        // window.addEventListener("beforeunload",()=>{
        //     sessionStorage.setItem("store", JSON.stringify(this.$store.state))
        // })
    },
    mounted(){
        this.$store.dispatch('user/getInfo')
        window.addEventListener('online',  this.updateOnlineStatus);
        window.addEventListener('offline', this.updateOnlineStatus);
        window.addEventListener('scroll', this.scrollFunc);  
        // window.addEventListener('resize', this.handleResize)
        this.createLoginPoint()
        if (this.$store.getters['user/isAdmin']) {
            this.getMessage()
            this.timer = setInterval(() => {
                this.getMessage();
            }, 1000 * 3);
        }

        // 获取选中文本
        if (this.$role('admin.top')) {
            setInterval(() => {
                this.getSelectionStr();
            }, 1000);
        }


    },
    methods: {
        ...mapActions(('stock'),['showStockDetail']),
        getSelectionStr(){
            this.selectStr = window.getSelection().toString()
            // if (selectStr && selectStr.length > 0) {
                
            // }
        },
        refreshPage() {
            this.$router.go(0)
        },
        handleResize({ width, height }) {
            // console.log(width);
            // this.$router.go(0)

            if (Math.abs(this.screenWidth - width) > 120) {
                // console.log('the screen change');
                Storage.setItem('screenWidth', width)
                this.$router.go(0)
            }
            // 
            // this.$router.replace({
            //     path: 'refresh', 
            // })
        },
        getMessage() {
            if (!this.$role('admin.top')) {
                return;
            }
            this.$store.commit('site/showLoading')
            this.$api.black.unread().then((result) => {
               this.newsCount = result.news
               this.researchsCount = result.researchs
               this.mediaCount = result.medias
            }).catch((err) => {

            }).finally(() => {
                this.$store.commit('site/hideLoading')
            })
        },
        addNewPost() {
            this.visible = true
        },
        onClose() {
            this.$store.commit('stock/hideStock');
        },
        menuClick(item) {
            this.$router.push({ name: item.key, params: { } })
        },
        scrollFunc() {
            const bodyScrollTop = document.documentElement.scrollTop;
            if (bodyScrollTop > 88) {
                document.getElementById("stock").style.top = '20px'
            } else {
                document.getElementById("stock").style.top = '88px'
            }
        },
        updateOnlineStatus(e) {
            if (e.type !== 'online') {
                this.openNewworkNotification();
            }
        },
        openNewworkNotification () {
            const key = `open${Date.now()}`;
            this.$notification.open({
                message: '网络连接错误',
                description: '请检查您的网络，网络连接错误将无法使用本站！',
                btn: (h)=>{
                    return h('a-button', {
                        props: {
                            type: 'primary',
                            size: 'small',
                        },
                        on: {
                            click: () => {
                                this.$notification.close(key);
                                // this.$router.replace({
                                //     name: 'admin-refresh', 
                                // })
                                this.refreshPage();
                            }
                        }
                    }, '刷新')
                },
                key,
                onClose: () => {},
            });
        },
    }
}
</script>

<style scoped lang="less">
#app {
    background: rgba(255,255,255,.2);
    color: #2c3e50;
    min-height: 100vh;
}

#sidebar {
    overflow: 'auto';
    min-height: 100vh;
    position: 'fixed' !important;
    left: 0;
    bottom: 0;
    overflow: hidden;

    .logo {
        text-align: left;
        color: #fff;
        font-size: 16px;
        text-align: center;
        height: 64px;
        line-height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;

        .logo-title {
            display: block;
            margin-left: 4px;
            height: 64px;
            line-height: 64px;
        }
        .logo-img {
            display: block;
        }
    }

    // .trigger {
    //     font-size: 16px;
    //     line-height: 64px;
    //     cursor: pointer;
    //     transition: color .3s;
    //     color: #fff;
    //     text-align: center;
    //     position: absolute;
    //     bottom: 20px;
    //     left: 32px;
    // }
}

#header {
    height: 64px;
    position: relative;
    z-index: 9;
    padding: 12px 24px 0 24px;
    // margin-bottom: 32px;
    box-sizing: border-box;
    border-bottom: 1px solid #eaecef;
    transition: all .2s ease-in-out;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    background: #fff;

    .menu-extra {
        position: absolute;
        right: 24px;
        top: 0;
        bottom: 0;
    }
}

#content {
    overflow: initial;
    // padding: 0 24px 24px 24px;
    // margin-top: 16px;
    // padding: 24px;
    background: #fff;
    min-height: 800px;
    position: relative;
    // width: 680px;

    .user {
        margin-top: 4px;
    }
}

#stock {
    position: fixed !important;
    top: 88px;
    right: 20px;
    bottom: 10px;
    overflow-y: auto;
    background: #fff;
    z-index: 999;

    .close {
        position: absolute;
        top: -5px;
        left: -5px;
        font-size: 20px;
        background: #fff;
        z-index: 9999;
        cursor: pointer;
    }
}


.menu-version {
    position: absolute;
    bottom: 48px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 32px;
    cursor: pointer;
    font-size: 10px;
    line-height: 32px;
    transition: color .3s;
    color: #fff;
    text-align: center;
}

.menu-operate {
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 48px;
    background-color: #002140;
    cursor: pointer;
    font-size: 16px;
    line-height: 48px;
    cursor: pointer;
    transition: color .3s;
    color: #fff;
    text-align: center;
}

.operate {
    text-align: center;
    width: 200px;
    position: fixed;
    bottom: 20px;
    left: 0;
}

.operate-collapsed {
    width: 80px;
}

#footer {
    text-align: right;
    border-top: 0.5px solid #cfcfcf;
    background: #f5f5f5;
    padding-right: 25px;
}

#loading {
    position: fixed;
    bottom: 20px;
    right: 20px;
}
</style>
