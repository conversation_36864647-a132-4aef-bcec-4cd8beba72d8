<template>
    <a-card title="白名单" :bordered=true size="small">
        <span slot="extra">
            <a-button class="fs12" size="small" @click="gotoOverview">速览</a-button>
            <!-- <a-button type="primary" class="fs12 ml16" size="small" @click="toogleRow">{{!toogle ? '详细': '简约'}}</a-button> -->
        </span>
        <!-- <a-button type="primary" size="small" slot="extra" @click="generatePicture">导出</a-button> -->
        <a-row :gutter="16" v-if="data && toogle">
            <a-col :span="24" v-for="(stock, key) in data" :key="key" class="web-white-item">
                <a-row :gutter="32">
                    <a-col :span="9">
                        <StockPopover 
                            :highlight="false" 
                            :source="stock.source" 
                            :stock="stock" 
                            :tag="true" 
                            :base="true"
                            :height="180">
                            <!-- <template slot="begin" v-if="stock.groupStocks[0]">
                                <span class="web-white-group">
                                   {{stock.groupStocks[0] && stock.groupStocks[0].group.id}}
                                </span>
                            </template>  -->
                            <template slot="end">
                                <span class="fr fs12" 
                                v-for="time in getReportTime(stock.reportTimes)" :key="time.id">
                                    {{time.date ? time.date.slice(4,6) : ''}}.{{time.date ? time.date.slice(6,) : ''}}({{time.name ? time.name.slice(6,) : ''}})
                                </span>
                            </template>
                        </StockPopover>
                    </a-col>
                    <a-col :span="8">
                        <StockIncome name="white" :stock=stock :width="width" />
                    </a-col>
                    <a-col :span="7">
                        <StockKline :stock=stock type="daily" :fromSina="true" />
                    </a-col>
                </a-row>
            </a-col>
        </a-row>
        <a-row :gutter="16" v-if="data && !toogle">
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" v-for="(stock, key) in data" :key="key">
                <StockPopover 
                    class="mb16"
                    :highlight="false" 
                    :source="stock.source" 
                    :stock="stock" 
                    :tag="true" 
                    :price="true"
                    :height="130">
                    <template slot="end">
                        <span class="fr fs12" 
                            v-for="time in getReportTime(stock.reportTimes)" :key="time.id">
                                {{time.date ? time.date.slice(4,6) : ''}}.{{time.date ? time.date.slice(6,) : ''}}({{time.name ? time.name.slice(6,) : ''}})
                        </span>
                    </template>
                </StockPopover>
            </a-col>
        </a-row>
        <a-pagination class="fr mt16" v-model="current" :total="total" :page-size="pageSize" @change="queryStocks" show-less-items />
        <!-- <div v-if="exports">
            <div class="g-white-export" v-for="(stocks, index) in sliceData(data)" :key="index">
                <a-row>
                    <a-col :span="24" v-for="(stock, key) in stocks" :key="key" class="item">
                        <span class="fs24 bold mr8">{{stock.code}}</span>
                        <span>{{stock.source.displayName}}</span>
                    </a-col>
                </a-row>
            </div>
        </div> -->
    </a-card>
</template>

<script>
import StockPopover from 'components/StockPopover'
import StockIncome from 'components/StockIncome'
import StockKline from 'components/StockKline'
import moment from 'moment'

export default {
    data() {
        return {
            data: [],
            current: 1,
            pageSize: 16,
            total: 0,
            exports: false,
            toogle: false,
            width: (window.bWidth - 180) / 24 * 8
        }
    },
    components: {
        StockPopover,
        StockIncome,
        StockKline
    },
    props: {},
    computed: {},
    created() {},
    mounted() {
        this.queryStocks();
    },
    methods: {
        gotoOverview() {
            const code_list = []
            this.data.forEach(element => {
                code_list.push(element.code)
            });

            this.$router.push({
                name: 'admin-quant-overview',
                query: {
                    list: code_list.join(',')
                }
            });
        },
        toogleRow() {
            this.toogle = !this.toogle
            this.pageSize = 16
            this.data = []
            this.queryStocks()
        },
        sliceData(data) {
            let arr = []
            for (let index = 0; index < data.length; index=index+10) {
                const element = data.slice(index, index + 10);
                arr.push(element)
            }

            return arr;
        },
        queryStocks() {
            this.$store.commit('site/showLoading')
            this.$api.stock.getStocks({
                white: true,
                pageSize: this.pageSize,
                pageIndex: this.current,
            }).then((result) => {
                this.data = result && result.stocks && Array.isArray(result.stocks.rows) ? result.stocks.rows : [];
                this.total = result && result.stocks && typeof result.stocks.count === 'number' ? result.stocks.count : 0;
            }).catch((err) => {

            }).finally(() => {
                this.$store.commit('site/hideLoading')
            });
        },
        getReportTime(list) {
            let result = list && list.filter((item) => {
                return item.date >= moment().format('YYYYMMDD')
            })

            return result
        },
         generatePicture() {
            this.exports = true;
			// const html2canvas = require('html2canvas');

            this.$nextTick(() => {
                const obj = document.querySelectorAll(".g-white-export")

                for (let index = 0; index < obj.length; index++) {
                    const element = obj[index];
                    const rect = element.getBoundingClientRect()

                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    const scale = window.devicePixelRatio || 1;
                    context.scale(scale, scale);
                    canvas.width = rect.width * scale;
                    canvas.height = rect.height * scale;
                    canvas.style.width = rect.width + "px";
                    canvas.style.height = rect.height + "px";

                    html2canvas(element, {
                        // scrollY: rect.top - 50,
                        // height: rect.height * scale,
                        // with: rect.width * scale,
                        // canvas: canvas,
                        // useCORS: true
                    }).then(canvas => {
                        // const url = canvas.toDataURL("image/png").replace(/^data:image\/[^;]+/, 'data:application/octet-stream');
                        // window.open(url, '_blank');
                        canvas.toBlob(blob => {
                            const imgUrl = URL.createObjectURL(blob)
                            const aImg = document.createElement('a')
                            aImg.href = imgUrl
                            aImg.download = `stocks-white${index}.jpeg`
                            document.body.appendChild(aImg)
                            aImg.click()
                            document.body.removeChild(aImg)
                        }, 'image/png');
                    }).finally(() => {
                        this.exports = false
                    })
                }
            })

		},
    },
}
</script>

<style scoped lang="less">
.web-white-item {
    margin-bottom: 32px;
    background: #f5f5f5;
}

.web-white-group {
    border-radius: 50%;
    background: cornflowerblue;
    color: #fff;
    width: 16px;
    height: 16px;
    line-height: 16px;
    display: inline-block;
    text-align: center;
    margin-right: 3px;

    a {
        color: #fff;
    }
}
.g-white-export {
    width: 540px;
    font-size: 18px;
    padding: 30px 20px;

    .item {
        text-align: left;
        padding: 6px;
        border-bottom: 1px solid #f5f5f5;
    }
}
</style>