<template>
    <div v-if="result">
        <a-row :gutter="16">
            <a-alert class="mb16" :message="'基准日: ' + result.quantConfig.decisionDate + '，部分量化数据6点-8点更新。'" banner />
            <!-- <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" class="mb16">
                <a-statistic title="央行公开市场交易" :value="result.daliy.funds">
                    <template #suffix>亿</template>
                </a-statistic>
                <div>{{result.daliy.fundsDesc}}</div>
            </a-col> -->
            <a-col :span="24" v-if="todayData.hot || lastData.hot"><h3>建议</h3></a-col>
            <a-col :xs="24" class="mb16">
                <span v-if="todayData.hot">{{todayData.hot}}</span>
                <span v-else>上个交易日:{{lastData.hot}}</span>
            </a-col>
            <a-divider></a-divider>
            <a-col :span="24"><h3>资金</h3></a-col>
            <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" class="mb16">
                <a-statistic title="shibor-O/N" :value="result.daliy.shiborON">
                <template #suffix>% <TaskCheck :task='18' /></template>
                </a-statistic>
            </a-col>
            <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" class="mb16">
                <a-statistic title="10年期国债(上个交易日)" :value="lastData.EMM00166466">
                    <template #suffix>% <TaskCheck :task='9' /></template>
                </a-statistic>
            </a-col>
            <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" class="mb16">
                <a-statistic title="10年期美债(上个交易日)" :value="lastData.EMG00001310">
                    <template #suffix>% <TaskCheck :task='9' /></template>
                </a-statistic>
            </a-col>
            <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" class="mb16">
                <a-statistic title="融资余额(上个交易日)" :value="lastData.rzye | numFilter(12, 2, '')">
                    <template #suffix>万亿 
                        <span class="mr4" v-html="replaceMethod(lastData.rzye - lastThirdData.rzye, lastData.rzye - lastThirdData.rzye, 8, 2, '')" />
                        <TaskCheck :task='8' />
                    </template>
                </a-statistic>
            </a-col>
            <!-- <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" class="mb16">
                <a-statistic v-if="result.daliy.isOpen && result.daliy.NorthMoney" title="北上资金" :value="result.daliy.NorthMoney">
                    <template #suffix>亿 <TaskCheck :task='7' /></template>
                </a-statistic>
                <a-statistic v-else title="北上资金(上个交易日)" :value="lastData.NorthMoney">
                    <template #suffix>亿 <TaskCheck :task='7' /></template>
                </a-statistic>
            </a-col> -->
            <a-divider></a-divider>
            <a-col :span="24"><h3>市场</h3></a-col>
            <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" class="mb16">
                <a-statistic v-if="result.daliy.isOpen" title="两市成交量(实时)" :value="result.daliy.volume | numFilter(8, 2, '')" :value-style="setStyle('volume')">
                    <template #suffix>亿 <TaskCheck :task='23' /></template>
                </a-statistic>
                <a-statistic v-else title="两市成交量(实时)">
                </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.medianincrease" title="涨跌幅中位数" :value="todayData.medianincrease">
                    <template #suffix>% </template>
                </a-statistic>
                <a-statistic v-else title="涨跌幅中位数(上个交易日)" :value="lastData.medianincrease"> </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.limitup" title="涨停" :value="todayData.limitup">
                    <template #suffix>家 </template>
                </a-statistic>
                <a-statistic v-else title="涨停(上个交易日)" :value="lastData.limitup"> </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.limitdown" title="跌停" :value="todayData.limitdown">
                    <template #suffix>家 </template>
                </a-statistic>
                <a-statistic v-else title="跌停(上个交易日)" :value="lastData.limitdown"> </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.newhigh" title="30日新高" :value="todayData.newhigh">
                    <template #suffix>家 </template>
                </a-statistic>
                <a-statistic v-else title="30日新高(上个交易日)" :value="lastData.newhigh"></a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.newlow" title="30日新低" :value="todayData.newlow">
                    <template #suffix>家 </template>
                </a-statistic>
                <a-statistic v-else title="30日新低(上个交易日)" :value="lastData.newlow"></a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.style" title="风格" :value="todayData.style" :valueStyle="{'font-size': '20px'}">
                </a-statistic>
                <a-statistic v-else title="风格(上个交易日)" :value="lastData.style" :valueStyle="{'font-size': '20px'}"></a-statistic>
            </a-col>
            <a-divider></a-divider>
            <a-col :span="24"><h3>指标</h3></a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="result.daliy.isOpen" title="赚钱指数(实时)" :value="result.statistics.more/result.statistics.total*100 | numFilter(0, 2, '')">
                    <template #suffix>%</template>
                </a-statistic>
                <a-statistic v-else title="赚钱指数(实时)">
                </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.fall3" :title='"恐慌(" + result.quantConfig.fall3 + ")"' :value="todayData.fall3" :value-style="setStyle('fall3')">
                    <template #suffix> </template>
                </a-statistic>
                <a-statistic v-else title="恐慌(上个交易日)" :value="lastData.fall3"></a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.rose3" :title='"贪婪(" + result.quantConfig.rose3 + ")"' :value="todayData.rose3" :value-style="setStyle('rose3')">
                    <template #suffix> </template>
                </a-statistic>
                <a-statistic v-else title="贪婪(上个交易日)" :value="lastData.rose3"></a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.crowding" :title='"拥挤度(" + result.quantConfig.crowding + ")"' :value="todayData.crowding" :value-style="setStyle('crowding')">
                    <template #suffix> </template>
                </a-statistic>
                <a-statistic v-else title="拥挤度(上个交易日)" :value="lastData.crowding"> </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.starcount" title="趋势" :value="todayData.starcount" :value-style="setStyle('starcount')">
                    <template #suffix> </template>
                </a-statistic>
                <a-statistic v-else title="趋势(上个交易日)" :value="lastData.starcount"> </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.SMB" title="SMB" :value="todayData.SMB">
                    <template #suffix> </template>
                </a-statistic>
                <a-statistic v-else title="SMB(上个交易日)" :value="lastData.SMB"> </a-statistic>
            </a-col>
            <a-col class="mb16" :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
                <a-statistic v-if="todayData.HML" title="HML" :value="todayData.HML">
                    <template #suffix> </template>
                </a-statistic>
                <a-statistic v-else title="HML(上个交易日)" :value="lastData.HML"> </a-statistic>
            </a-col>
            <a-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6" v-if="result.worthLast" v-role="['admin.top']">
                <a-statistic :title="'净值(' + result.worthLast.date + ')'" :value="result.worthLast.worths">
                    <template #suffix>
                        <span v-if="result.daliy.isOpen" class="mr4" v-html="replaceMethod(result.worthCurrent.worths - result.worthLast.worths, (result.worthCurrent.worths - result.worthLast.worths)*100, 0, 2, '%')" />
                        <TaskCheck :task='30' />
                    </template>
                </a-statistic>
            </a-col>
        </a-row>
    </div>
</template>

<script>

import moment from 'moment';
import TaskCheck from 'components/TaskCheck'
import StockTable from 'components/StockTable'


export default {
    data() {
        return {
            // bx: null,
            todayData: null,
            lastData: null,
            lastThirdData: null,
            result: null,
        }
    },
    components: {
        TaskCheck,
        StockTable
    },
    props: {
    },
    computed: {
    },
    created() {},
    watch: {
    },
    destroyed () {
    },
    mounted() {
        this.fetchData()
        // this.getBX()
    },
    methods: {
        fetchData() {
            this.$store.commit('site/showLoading')
            this.$api.black.home({
                key: 'daliyVolume,statistics,worthLast,worthCurrent,daliy,quantConfig',
            }).then((result) => {
                this.result = result;
                this.todayData = result.daliyVolume[result.daliyVolume.length - 1]
                this.lastData = result.daliyVolume[result.daliyVolume.length - 2]
                this.lastThirdData = result.daliyVolume[result.daliyVolume.length - 3]
            }).catch((err) => {

            }).finally(() => {
                this.$store.commit('site/hideLoading')
            })
        },
        // getBX() {
        //     this.$api.black.getChart({type: 3}).then((result) => {
        //         this.bx = result
        //     })
        // },
        setStyle(type) {
            if (type === 'volume') {
                if (this.result.daliy.volume > 1000000000000) {
                    return { color: '#cf1322' }
                }
            }
            if (type === 'fall3') {
                if (this.result.daliy.fall3 >= this.result.quantConfig.fall3) {
                    return { color: '#cf1322' }
                }
            }
            if (type === 'rose3') {
                if (this.result.daliy.rose3 >= this.result.quantConfig.rose3) {
                    return { color: '#cf1322' }
                }
            }
            if (type === 'crowding') {
                if (this.result.daliy.crowding >= this.result.quantConfig.crowding) {
                    return { color: '#cf1322' }
                }
            }
        }
    },
}
</script>

<style scoped lang="less">

</style>