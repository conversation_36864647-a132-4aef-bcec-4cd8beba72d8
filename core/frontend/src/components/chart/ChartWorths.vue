<template>
    <a-card title="净值趋势图" size="small" :loading="loading" class="chart-card">
        <div ref="chartContainer" id="chart-worths" class="chart-container"></div>
    </a-card>
</template>

<script>
var echarts = require('echarts');
export default {
    data() {
        return {
            worthsDate: [],
            worthsData: [],
            baseWorth: 0,
            loading: true,
            maxWorth: 0,
            minWorth: 0,
            lastWorth: 0,
            myChart: null,
            renderRetryCount: 0
        }
    },
    components: {},
    props: {},
    computed: {},
    created() {},
    mounted() {
        this.$nextTick(() => {
            this.fetchData();
        });
        // window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
        // window.removeEventListener('resize', this.handleResize);
        // if (this.myChart) {
        //     this.myChart.dispose();
        //     this.myChart = null;
        // }
    },
    methods: {
        fetchData() {
            this.loading = true;
            this.$api.worth.getWorths({
                pageIndex: 1,
                pageSize: 500
            }).then((result) => {
                if (result && result.length > 0) {
                    this.worthsDate = [];
                    this.worthsData = [];
                    result.reverse().forEach(element => {
                        this.worthsDate.push(element.date);
                        const worth = Number(element.worths);
                        this.worthsData.push(isNaN(worth) ? 0 : worth);
                    });
                    const recentData = this.worthsData.slice(-Math.floor(this.worthsData.length * 0.5));
                    this.baseWorth = Math.max(...recentData) * 0.8;
                    this.maxWorth = Math.max(...this.worthsData);
                    this.minWorth = Math.min(...this.worthsData);
                    this.lastWorth = this.worthsData[this.worthsData.length - 1];
                    this.renderWorthsChart();
                } else {
                    this.$message.info('暂无净值数据');
                }
            }).catch((err) => {
                this.$message.error('数据加载失败: ' + (err.message || '未知错误'));
            }).finally(() => {
                this.loading = false;
            });
        },
        handleResize() {
            if (this.myChart) {
                this.myChart.resize();
            }
        },
        renderWorthsChart() {
            let chartDom = this.$refs.chartContainer;
            if (!chartDom) {
                chartDom = document.getElementById('chart-worths');
            }

            if (!chartDom) {
                if (this.renderRetryCount < 5) {
                    this.renderRetryCount++;
                    setTimeout(() => {
                        this.renderWorthsChart();
                    }, 500);
                } else {
                    this.$message.error('图表容器不存在，无法渲染图表');
                }
                return;
            }

            const rect = chartDom.getBoundingClientRect();

            if (rect.width === 0 || rect.height === 0) {
                if (this.renderRetryCount < 5) {
                    this.renderRetryCount++;
                    chartDom.style.width = '100%';
                    chartDom.style.height = '400px';
                    setTimeout(() => {
                        this.renderWorthsChart();
                    }, 500);
                } else {
                    chartDom.style.width = '100%';
                    chartDom.style.height = '400px';
                    this.$message.error('图表容器尺寸无效，无法渲染图表');
                }
                return;
            }

            this.renderRetryCount = 0;

            if (this.myChart) {
                this.myChart.dispose();
                this.myChart = null;
            }

            try {
                this.myChart = echarts.init(chartDom, 'default');

                const colorLinear = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0,
                        color: '#ff4d4f'
                    }, {
                        offset: 1,
                        color: 'rgba(255, 77, 79, 0.1)'
                    }]
                };

                const option = {
                    title: {
                        text: '净值趋势图',
                        left: 'center',
                        textStyle: {
                            fontSize: 16,
                            fontWeight: 'bold'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999',
                                width: 1,
                                type: 'dashed'
                            }
                        },
                        formatter: (params) => {
                            const param = params[0];
                            const date = this.worthsDate[param.dataIndex];
                            const value = Number(param.value);
                            const formattedValue = isNaN(value) ? '无效数据' : value.toFixed(4);
                            let maxMinMark = '';
                            if (!isNaN(value) && value === this.maxWorth) {
                                maxMinMark = '<div style="color: #ec0000;">(最大值)</div>';
                            } else if (!isNaN(value) && value === this.minWorth) {
                                maxMinMark = '<div style="color: #1890ff;">(最小值)</div>';
                            }
                            return `
                                <div style="background: rgba(255, 255, 255, 0.9); padding: 10px; border-radius: 4px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);">
                                    <div style="font-weight: bold; margin-bottom: 5px;">${date}</div>
                                    <div style="color: #ff4d4f;">净值: ${formattedValue}</div>
                                    ${maxMinMark}
                                </div>
                            `;
                        }
                    },
                    toolbox: {
                        feature: {
                            dataView: {show: true, readOnly: false},
                            magicType: {show: true, type: ['line', 'bar']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    legend: {
                        show: false,
                        data:['净值'],
                        bottom: 0
                    },
                    dataZoom: [{
                        show: true,
                        realtime: true,
                        start: 50,
                        end: 100
                    }, {
                        type: 'inside',
                        realtime: true,
                        start: 50,
                        end: 100
                    }],
                    xAxis: [
                        {
                            type: 'category',
                            data: this.worthsDate,
                            axisPointer: {
                                type: 'shadow'
                            },
                            axisLabel: {
                                rotate: 0,
                                interval: Math.max(1, Math.floor(this.worthsDate.length / 10)),
                                fontSize: 12
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '净值',
                            min: 0.6,
                            axisLabel: {
                                formatter: '{value}'
                            },
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed'
                                }
                            }
                        }
                    ],
                    series: [
                        {
                            name:'净值',
                            type:'line',
                            data: this.worthsData,
                            smooth: true,
                            showSymbol: false,
                            lineStyle: {
                                width: 2,
                                color: '#ff4d4f'
                            },
                            areaStyle: {
                                color: colorLinear
                            },
                            markLine: {
                                symbol: ['none', 'none'],
                                data: [{
                                    yAxis : this.baseWorth,
                                    lineStyle: {
                                        type: 'dashed',
                                        color: 'blue',
                                        opacity: 0.4
                                    },
                                    label: {
                                        show: true,
                                        position: 'end',
                                        formatter: '回撤20%线'
                                    }
                                }]
                            },
                            emphasis: {
                                focus: 'series',
                                lineStyle: {
                                    width: 4
                                }
                            }
                        }
                    ]
                };
                this.myChart.setOption(option);
            } catch (error) {
                this.$message.error('图表渲染失败: ' + (error.message || '未知错误'));
            }
        }
    }
}
</script>

<style scoped>
.chart-card {
    min-height: 450px;
    display: flex;
    flex-direction: column;
}

#chart-worths {
    width: 100%;
    height: 400px;
    min-height: 400px;
    display: block;
    box-sizing: border-box;
    flex: 1;
}
</style>