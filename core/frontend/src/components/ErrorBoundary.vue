<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <a-icon type="exclamation-circle" />
        </div>
        <h3 class="error-title">{{ errorTitle }}</h3>
        <p class="error-message">{{ errorMessage }}</p>
        
        <div class="error-actions">
          <a-button type="primary" @click="retry">
            <a-icon type="reload" />
            重试
          </a-button>
          <a-button @click="goHome">
            <a-icon type="home" />
            返回首页
          </a-button>
          <a-button v-if="showDetails" type="link" @click="toggleDetails">
            {{ showErrorDetails ? '隐藏' : '显示' }}详情
          </a-button>
        </div>

        <div v-if="showErrorDetails && errorDetails" class="error-details">
          <a-collapse>
            <a-collapse-panel header="错误详情" key="1">
              <pre class="error-stack">{{ errorDetails.stack }}</pre>
            </a-collapse-panel>
            <a-collapse-panel header="组件信息" key="2">
              <div class="error-info">
                <p><strong>组件:</strong> {{ errorDetails.componentName }}</p>
                <p><strong>时间:</strong> {{ errorDetails.timestamp }}</p>
                <p><strong>用户代理:</strong> {{ errorDetails.userAgent }}</p>
                <p><strong>页面URL:</strong> {{ errorDetails.url }}</p>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </div>
    
    <div v-else>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorBoundary',
  props: {
    fallbackComponent: {
      type: [Object, Function],
      default: null
    },
    onError: {
      type: Function,
      default: null
    },
    showDetails: {
      type: Boolean,
      default: process.env.NODE_ENV === 'development'
    }
  },
  data() {
    return {
      hasError: false,
      errorDetails: null,
      showErrorDetails: false,
      retryCount: 0,
      maxRetries: 3
    }
  },
  computed: {
    errorTitle() {
      if (this.retryCount >= this.maxRetries) {
        return '系统暂时无法正常工作'
      }
      return '页面加载出现问题'
    },
    errorMessage() {
      if (this.retryCount >= this.maxRetries) {
        return '我们正在努力修复这个问题，请稍后再试或联系技术支持。'
      }
      return '抱歉，页面遇到了一些问题。您可以尝试刷新页面或返回首页。'
    }
  },
  errorCaptured(err, instance, info) {
    this.captureError(err, instance, info)
    return false // 阻止错误继续传播
  },
  mounted() {
    // 监听全局错误
    window.addEventListener('error', this.handleGlobalError)
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection)
  },
  beforeDestroy() {
    window.removeEventListener('error', this.handleGlobalError)
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection)
  },
  methods: {
    captureError(error, instance, info) {
      this.hasError = true
      
      const errorDetails = {
        message: error.message,
        stack: error.stack,
        componentName: instance?.$options.name || 'Unknown',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        info: info,
        retryCount: this.retryCount
      }
      
      this.errorDetails = errorDetails
      
      // 调用自定义错误处理函数
      if (this.onError) {
        this.onError(error, errorDetails)
      }
      
      // 发送错误报告到服务器
      // this.reportError(errorDetails)
      
      // 在开发环境下打印错误
      if (process.env.NODE_ENV === 'development') {
        console.error('ErrorBoundary captured error:', error)
        console.error('Component info:', info)
      }
    },
    
    handleGlobalError(event) {
      const error = event.error || new Error(event.message)
      this.captureError(error, null, 'Global Error')
    },
    
    handleUnhandledRejection(event) {
      const error = event.reason instanceof Error ? event.reason : new Error(event.reason)
      this.captureError(error, null, 'Unhandled Promise Rejection')
    },
    
    retry() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        this.hasError = false
        this.errorDetails = null
        this.showErrorDetails = false
        
        // 强制重新渲染子组件
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      } else {
        this.$message.warning('重试次数已达上限，请稍后再试')
      }
    },
    
    goHome() {
      this.$router.push('/')
    },
    
    toggleDetails() {
      this.showErrorDetails = !this.showErrorDetails
    },
    
    reportError(errorDetails) {
      // 发送错误报告到服务器
      if (navigator.sendBeacon) {
        const data = JSON.stringify({
          type: 'error',
          ...errorDetails
        })
        navigator.sendBeacon('/api/errors', data)
      } else {
        // 降级方案
        fetch('/api/errors', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            type: 'error',
            ...errorDetails
          })
        }).catch(() => {
          // 忽略报告错误的错误
        })
      }
    },
    
    reset() {
      this.hasError = false
      this.errorDetails = null
      this.showErrorDetails = false
      this.retryCount = 0
    }
  }
}
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-icon {
  font-size: 64px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.error-message {
  font-size: 16px;
  color: #595959;
  margin-bottom: 24px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 24px;
}

.error-details {
  text-align: left;
  margin-top: 24px;
}

.error-stack {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-info p {
  margin-bottom: 8px;
  font-size: 14px;
}

.error-info strong {
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-container {
    min-height: 300px;
    padding: 16px;
  }
  
  .error-icon {
    font-size: 48px;
  }
  
  .error-title {
    font-size: 20px;
  }
  
  .error-message {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .ant-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .error-title {
    color: #f0f0f0;
  }
  
  .error-message {
    color: #d9d9d9;
  }
  
  .error-stack {
    background: #1f1f1f;
    color: #f0f0f0;
  }
  
  .error-info strong {
    color: #f0f0f0;
  }
}
</style>
