import Vue from 'vue'
import App from './App.vue'
import router from './router';
import store from './store';
import './styles/global.less';
import './directives/permission'
import './directives/role'
// import './registerServiceWorker'
import './plugins'
import PerformancePlugin from './plugins/performance';
import {commonMixin} from './mixins/commonMixin'

Vue.mixin(commonMixin);

// 安装性能优化插件
// 根据设备类型调整性能配置
const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

Vue.use(PerformancePlugin, {
  enablePerformanceMonitoring: !isMobile, // 移动端禁用性能监控
  enableComponentCache: true, // 移动端启用组件缓存
  enableDOMOptimization: true,
  enableOfflineSupport: false, // 移动端禁用离线支持
  enableMemoryMonitoring: false, // 移动端禁用内存监控
  router
})

// 在开发模式下加载缓存修复工具和vConsole调试工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/cacheFixScript.js');
  // 引入并初始化vConsole
  import('vconsole').then(VConsole => {
    const vConsole = new VConsole.default();
    console.log('vConsole initialized');
  });
}

Vue.config.productionTip = false

window.addEventListener("message", (e) => {
  const method = e.data && e.data.method

  if (method === 'getUserInfo') {
    const userInfo = store.getters['user/userInfo'];
    window.parent && window.parent.postMessage(userInfo, "*");
  }
})

// Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive.
// document.addEventListener('touchmove', function (event) {
//   event.preventDefault();
// }, {
//   passive: false
// });

new Vue({
  router,
  store,
  render: h => h(App),
  mounted() {
    
  },
}).$mount('#app')
