{"name": "frontend", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --host 0.0.0.0", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "2.1.5", "@tiptap/extension-image": "2.12.0", "@tiptap/extension-text-style": "2.12.0", "@tiptap/pm": "2.12.0", "@tiptap/starter-kit": "2.12.0", "@tiptap/vue-2": "2.12.0", "core-js": "^3.8.3", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "mavon-editor": "^2.10.4", "pdfjs-dist": "2.5.207", "prosemirror-model": "1.19.0", "qs": "^6.11.2", "register-service-worker": "^1.6.2", "tributejs": "^5.1.3", "vue-infinite-loading": "^2.4.5", "vue-json-viewer": "2", "vue-moment": "^4.1.0", "vue-pdf": "4.2.0", "vue-simple-context-menu": "3.4.2", "vue-socket.io": "^3.0.10", "vue-touch": "^2.0.0-beta.4", "vue-tribute": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-pwa": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^3.8.1", "less-loader": "^4.1.0", "style-resources-loader": "^1.5.0", "vconsole": "^3.15.1", "vue-template-compiler": "2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}