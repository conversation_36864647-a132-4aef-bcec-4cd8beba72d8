import asyncio
from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig
from crawl4ai.cache_context import CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

config = CrawlerRunConfig(
    css_selector=".stockquote, .bp2sr",
    cache_mode=CacheMode.DISABLED  # 禁用缓存
)

async def main():

    # 定义提取策略
    # strategy = LLMExtractionStrategy(
    #     provider="ollama/llama2",  # 可以使用本地模型
    #     api_token="your-api-token",
    #     instruction="""
    #     从这个网页中提取所有产品信息，包括：
    #     1. 产品名称
    #     2. 价格
    #     3. 产品描述
    #     4. 用户评分
    #     请以JSON格式返回结果。
    #     """
    # )

    # 创建内容过滤器
    # content_filter = PruningContentFilter(
    #     threshold=0.48,  # 内容相关性阈值
    #     threshold_type="fixed",
    #     min_word_threshold=0
    # )

    # 定义精准的股票提取规则
    schema = {
        "name": "股票信息",
        "baseSelector": ".stockquote",
        "fields": [
            {"name": "股票名称", "selector": ".stockname > h1", "type": "text"},
            {"name": "股票代码", "selector": ".stockcode", "type": "text"},
            {"name": "当前价格", "selector": ".newprice > span:nth-child(1) > span", "type": "text"},
            {"name": "涨跌幅", "selector": ".newprice > span:nth-child(2)", "type": "text"},
            {"name": "开盘价", "selector": ".openprice", "type": "text"},
            {"name": "最高价", "selector": ".highprice", "type": "text"},
            {"name": "最低价", "selector": ".lowprice", "type": "text"},
            {"name": "成交量", "selector": ".volume", "type": "text"},
            {"name": "成交额", "selector": ".amount", "type": "text"}
        ]
    }

    extraction_strategy = JsonCssExtractionStrategy(schema)

    # 初始化爬虫
    async with AsyncWebCrawler(
        browser_type="chromium",  # 使用Chromium浏览器
        headless=True,
        verbose=True
    ) as crawler:
        # 爬取指定URL
        # 使用配置对象运行爬虫
        # 爬取指定URL并应用提取策略
        try:
            result = await crawler.arun(
                url="https://xueqiu.com/S/SH688256",  # 恢复到正确的股票页面URL
                config=config,
                # extraction_strategy=extraction_strategy,
                timeout=30000  # 增加超时时间
            )
            # 打印结构化提取结果
            # 检查结果对象中的可用属性
            print("结果对象属性:", dir(result))
            # 尝试获取提取的数据
            if hasattr(result, 'data'):
                print("结构化股票信息:")
                print(result.data)
            elif hasattr(result, 'extracted'):
                print("结构化股票信息:")
                print(result.extracted)
            else:
                print("未找到提取的数据属性")
        except Exception as e:
            print(f"爬取过程中发生错误: {e}")
        # 打印爬取结果（markdown格式）
        print("爬取结果:")
        print(result.markdown)
        
        # 可以将结果保存到文件
        with open("crawl_result.md", "w", encoding="utf-8") as f:
            f.write(result.markdown)
        print("结果已保存到crawl_result.md")

if __name__ == "__main__":
    asyncio.run(main())