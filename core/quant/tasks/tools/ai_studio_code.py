import numpy as np
import pandas as pd
from scipy import stats
import talib as ta
import statsmodels.api as sm

class StockOldAnalyzer:
    """股票分析器类，提供多种技术分析和量化指标计算功能（优化版）"""
    def __init__(self, data, config=None):
        """初始化StockOldAnalyzer实例

        Args:
            data (pd.DataFrame): 股票数据DataFrame
            config (dict, optional): 配置字典，可指定列名等参数
        """
        self.data = data
        self.config = config or {}
        self.date_column = self.config.get('date_column', 'date')
        self.close_column = self.config.get('close_column', 'close')
        self.high_column = self.config.get('high_column', 'high')
        self.low_column = self.config.get('low_column', 'low')
        self.volume_column = self.config.get('volume_column', 'volume')
        self.open_column = self.config.get('open_column', 'open')
        self.amount_column = self.config.get('amount_column', 'amount')
        self.fast_calculator = None
        
        # 预计算常用指标，避免重复计算
        if not self.data.empty:
            self._pre_calculate_indicators()

    def _pre_calculate_indicators(self):
        """预先计算整个数据集的常用技术指标"""
        try:
            self.data['ma5'] = ta.MA(self.data[self.close_column], timeperiod=5)
            self.data['ma10'] = ta.MA(self.data[self.close_column], timeperiod=10)
            self.data['ma20'] = ta.MA(self.data[self.close_column], timeperiod=20)
            self.data['ma30'] = ta.MA(self.data[self.close_column], timeperiod=30)
            self.data['ma60'] = ta.MA(self.data[self.close_column], timeperiod=60)
            self.data['atr14'] = ta.ATR(self.data[self.high_column], self.data[self.low_column], self.data[self.close_column], timeperiod=14)
            self.data['rsi14'] = ta.RSI(self.data[self.close_column], timeperiod=14)
            self.data['obv'] = ta.OBV(self.data[self.close_column], self.data[self.volume_column])
            # 计算MACD
            dif, dea, hist = ta.MACD(self.data[self.close_column], fastperiod=12, slowperiod=26, signalperiod=9)
            self.data['macd_dif'] = dif
            self.data['macd_dea'] = dea
            self.data['macd_hist'] = hist
        except Exception as e:
            print(f"预计算指标失败: {e}")


    def _cal_limit_num(self):
        """计算股票连板数量"""
        try:
            limit = 0
            if len(self.data) < 2: return 0
            # 涨停定义为收盘价相比前一收盘价上涨约10%（或20%），且收盘价等于最高价
            for i in range(1, min(10, len(self.data))):
                today = self.data.iloc[-i]
                yesterday = self.data.iloc[-(i + 1)]
                price_limit_ratio = today[self.close_column] / yesterday[self.close_column] - 1
                is_price_limit = 0.098 < price_limit_ratio < 0.102 or 0.198 < price_limit_ratio < 0.202
                if is_price_limit and today[self.close_column] == today[self.high_column]:
                    limit += 1
                else:
                    break
            return limit
        except Exception as e:
            # print(f"计算连板数失败: {e}")
            return 0

    def _cal_price_momentum(self, period):
        """计算指定周期的价格动量"""
        try:
            if len(self.data) < period + 1: return 0
            return (self.data[self.close_column].iloc[-1] / self.data[self.close_column].iloc[-period-1]) - 1
        except Exception as e:
            # print(f"计算价格动量失败: {e}")
            return 0

    def _cal_bottom_return(self):
        """计算从底部到当前的收益率"""
        try:
            if len(self.data) < 100: return self._cal_price_momentum(len(self.data)-2)
            min_price = self.data[self.low_column].rolling(100).min().iloc[-1]
            if min_price == 0: return 0
            return (self.data[self.close_column].iloc[-1] - min_price) / min_price
        except Exception as e:
            # print(f"计算底部收益率失败: {e}")
            return 0
            
    # 其他未大幅修改的基础方法保持不变...
    # _cal_isTup, _cal_k, _cal_decision_date_return etc.
    # ... (为了简洁，省略了未核心修改的代码，但在最终成品中应保留)

    def analyze_stock(self, code, df=None):
        """分析股票并返回StockOldAnalysis对象"""
        try:
            from .data_models import StockOldAnalysis
            
            if df is not None:
                self.data = df
                self._pre_calculate_indicators()

            if len(self.data) < 60:
                 # print(f"数据量不足，无法分析股票 {code}")
                 return StockOldAnalysis(code=code) # 返回一个默认空对象
            
            # 重新计算所有指标
            limit = self._cal_limit_num()
            # isBottomInversion = self._cal_bottom_inversion() # 可以被更精确的信号替代
            # isHeavyVolume = self._cal_isHeavyVolume() # 可以被更精确的信号替代
            capm_result = self._cal_CAPM() # 假设此方法保留
            macd_signal = self._cal_macd() # MACD信号优化
            ma_signal = self._cal_ma() # MA信号优化
            
            # 新增四个核心量化信号
            isStrongTrend = self._cal_strong_trend()
            isBreakoutPlatform = self._cal_breakout_platform()
            isBigDivergence = self._cal_big_divergence()
            isFundAccumulation = self._cal_fund_accumulation()
            buySignal = self._cal_buy_signal(isStrongTrend, isBreakoutPlatform, isBigDivergence, isFundAccumulation)

            # ... 其他指标计算
            ret10 = self._cal_price_momentum(10)
            ret20 = self. _cal_price_momentum(20)
            # ...

            # 创建并返回StockOldAnalysis对象
            # 注意：返回的对象结构不变
            return StockOldAnalysis(
                code=code,
                limit=limit,
                ret10=round(ret10, 4),
                ret20=round(ret20, 4),
                # ... 其他字段
                macd=macd_signal,
                ma=ma_signal,
                strongTrend=isStrongTrend,
                breakoutPlatform=isBreakoutPlatform,
                bigDivergence=isBigDivergence,
                fundAccumulation=isFundAccumulation,
                buySignal=buySignal
                # ... 填充所有StockOldAnalysis所需的字段
            )
        except Exception as e:
            print(f"分析股票 {code} 失败: {e}")
            from .data_models import StockOldAnalysis
            return StockOldAnalysis(code=code)


    def _cal_macd(self):
        """
        [优化] 计算MACD信号.
        - 信号1 (金叉买点): DIF在0轴下方或附近上穿DEA，且成交量配合放大。
        - 信号2 (底背离买点): 价格创近期新低，但DIF未创新低，随后形成金叉。
        """
        try:
            if len(self.data) < 60 or 'macd_dif' not in self.data.columns:
                return False

            # 获取最近的数据
            last = self.data.iloc[-1]
            prev = self.data.iloc[-2]

            dif_last, dea_last, hist_last = last['macd_dif'], last['macd_dea'], last['macd_hist']
            dif_prev, dea_prev = prev['macd_dif'], prev['macd_dea']

            # 1. 寻找金叉
            # 条件：DIF由下向上穿过DEA，且发生在0轴下方或刚上0轴，且柱子开始变红
            is_golden_cross = dif_prev <= dea_prev and dif_last > dea_last
            is_near_zero = dea_last < 0.1 # 发生在0轴下方或刚突破一点点
            is_hist_turn_positive = hist_last > 0

            if is_golden_cross and is_near_zero and is_hist_turn_positive:
                # 确认成交量 - 金叉日成交量 > 5日均量
                vol_last = last[self.volume_column]
                vol_ma5 = self.data[self.volume_column].rolling(5).mean().iloc[-1]
                if vol_last > vol_ma5:
                    return True

            # 2. 寻找底背离 (更严格的定义)
            # 在过去30-40天内寻找
            search_period = 40
            if len(self.data) < search_period + 2: return False
            
            data_period = self.data.iloc[-search_period:]
            
            # 找到价格的局部低点索引
            low_indices = data_period.index[
                (data_period[self.low_column] == data_period[self.low_column].rolling(5, center=True).min())
            ]
            if len(low_indices) < 2: return False

            # 取最近的两个价格低点
            low1_idx, low2_idx = low_indices[-2], low_indices[-1]
            price_low1, price_low2 = self.data.loc[low1_idx, self.low_column], self.data.loc[low2_idx, self.low_column]
            dif_low1, dif_low2 = self.data.loc[low1_idx, 'macd_dif'], self.data.loc[low2_idx, 'macd_dif']

            # 判断背离：价格创新低，DIF没有创新低
            price_divergence = price_low2 < price_low1 * 0.98  # 价格创新低
            macd_not_divergence = dif_low2 > dif_low1 # DIF没有创新低
            
            # 确认信号：背离发生后，近期（最后几天）出现金叉
            if price_divergence and macd_not_divergence:
                # 检查最近5天内是否发生了金叉
                recent_cross = (self.data['macd_dif'].iloc[-5:] > self.data['macd_dea'].iloc[-5:]).any() and \
                               (self.data['macd_dif'].iloc[-6] <= self.data['macd_dea'].iloc[-6])
                if recent_cross:
                    return True

            return False
        except Exception as e:
            # print(f"MACD计算失败: {e}")
            return False

    def _cal_ma(self):
        """
        [优化] 计算均线信号.
        判断是否形成稳定、清晰的多头排列趋势，并刚刚进入可买入区域。
        """
        try:
            if len(self.data) < 35 or 'ma5' not in self.data.columns:
                return False

            last = self.data.iloc[-1]
            prev = self.data.iloc[-2]

            # 条件1: 均线多头排列 (硬性条件)
            is_bullish_arrangement = last['ma5'] > last['ma10'] and last['ma10'] > last['ma20'] and last['ma20'] > last['ma60']
            if not is_bullish_arrangement:
                return False

            # 条件2: 均线斜率健康向上
            # 使用线性回归计算最近10天MA10的斜率，确保趋势稳定
            y = self.data['ma10'].iloc[-10:].values
            x = np.arange(len(y))
            slope = np.polyfit(x, y, 1)[0]
            if slope <= 0: # 斜率必须为正
                return False

            # 条件3: 价格在均线上方，但并未过度偏离 (寻找回调买点)
            # 价格在MA10之上，且刚刚从MA10附近回升
            is_above_ma10 = last[self.close_column] > last['ma10']
            # 前几天可能触及或接近MA10
            was_near_ma10 = (self.data[self.low_column].iloc[-5:] <= self.data['ma10'].iloc[-5:] * 1.01).any()
            
            # 乖离率不能过大，防止追高
            bias = (last[self.close_column] / last['ma20'] - 1) * 100
            if bias > 15: # 距离20日线超过15%则过高
                return False

            # 条件4: 成交量配合 - 最近上涨日成交量 > 最近下跌日成交量
            recent_data = self.data.iloc[-5:]
            up_days_vol = recent_data[recent_data[self.close_column] > recent_data[self.open_column]][self.volume_column].mean()
            down_days_vol = recent_data[recent_data[self.close_column] < recent_data[self.open_column]][self.volume_column].mean()

            # 填充NaN防止错误
            up_days_vol = up_days_vol if pd.notna(up_days_vol) else 0
            down_days_vol = down_days_vol if pd.notna(down_days_vol) else 1 # 设为1防止除0
            
            volume_healthy = up_days_vol > down_days_vol * 1.2

            return is_bullish_arrangement and slope > 0 and is_above_ma10 and was_near_ma10 and volume_healthy

        except Exception as e:
            # print(f"计算均线信号失败: {e}")
            return False

    def _cal_strong_trend(self):
        """
        [核心优化] 计算强势趋势指标.
        一个股票被认为是强势趋势，必须满足均线多头排列，趋势稳定，且量价关系健康。
        """
        try:
            if len(self.data) < 60: return False
            
            last = self.data.iloc[-1]
            
            # 1. 均线系统呈“多头排列”
            ma_bullish = last['ma5'] > last['ma10'] > last['ma20'] > last['ma60']
            if not ma_bullish: return False

            # 2. 均线斜率稳定向上 (检查多条均线)
            for period in [5, 10, 20]:
                y = self.data[f'ma{period}'].iloc[-10:].values
                x = np.arange(len(y))
                if np.polyfit(x, y, 1)[0] <= 0:
                    return False

            # 3. 价格沿MA5或MA10健康上行
            price_on_ma = last[self.close_column] > last['ma5']
            # 最近10天，价格跌破MA10的天数不能超过2天
            break_ma10_days = (self.data[self.close_column].iloc[-10:] < self.data['ma10'].iloc[-10:]).sum()
            if not price_on_ma or break_ma10_days > 2:
                return False

            # 4. 波动率(ATR)受控，不能是失控的疯涨
            atr_ratio = last['atr14'] / last[self.close_column]
            if atr_ratio > 0.07: # 日均波动超过7%可能过热
                return False

            # 5. 量价关系健康: 上涨放量，下跌缩量
            # 最近20天，上涨日的平均成交量 > 下跌日的平均成交量
            recent_data = self.data.iloc[-20:].copy()
            recent_data['change'] = recent_data[self.close_column].diff()
            avg_vol_up = recent_data[recent_data['change'] > 0][self.volume_column].mean()
            avg_vol_down = recent_data[recent_data['change'] <= 0][self.volume_column].mean()
            if not (pd.notna(avg_vol_up) and pd.notna(avg_vol_down) and avg_vol_up > avg_vol_down * 1.2):
                return False
                
            return True
        except Exception as e:
            # print(f"计算强势趋势股失败: {e}")
            return False

    def _cal_breakout_platform(self):
        """
        [核心优化] 计算平台突破指标.
        识别一个明确的、长期的盘整平台，并捕捉到带量的有效突破。
        """
        try:
            if len(self.data) < 60: return False
            
            # 1. 寻找一个长为30-60天的盘整平台
            platform_period = 40
            platform_data = self.data.iloc[-platform_period-5:-5] # 在最近5天前寻找平台
            if len(platform_data) < 30: return False
            
            platform_high = platform_data[self.high_column].max()
            platform_low = platform_data[self.low_column].min()
            platform_vol_avg = platform_data[self.volume_column].mean()

            # 2. 平台振幅必须足够小
            if (platform_high - platform_low) / platform_low > 0.20: # 振幅大于20%不算平台
                return False

            # 3. 寻找突破信号 (最近5天内发生)
            breakout_data = self.data.iloc[-5:]
            
            # 条件a: 收盘价有效突破平台高点
            breakout_day = None
            for i in range(1, 6):
                day_to_check = self.data.iloc[-i]
                if day_to_check[self.close_column] > platform_high * 1.01: # 收盘价高出1%
                    breakout_day_index = -i
                    breakout_day = day_to_check
                    break
            if breakout_day is None: return False

            # 条件b: 突破日成交量显著放大
            if breakout_day[self.volume_column] < platform_vol_avg * 1.8: # 要求成交量放大1.8倍
                return False

            # 条件c: 突破后没有立即跌回
            # 突破日之后的所有日子，最低价不能低于平台高点
            data_after_breakout = self.data.iloc[breakout_day_index+1:]
            if not data_after_breakout.empty:
                if (data_after_breakout[self.low_column] < platform_high).any():
                    return False
            
            # 条件d: 当前价格仍维持在突破位之上
            if self.data[self.close_column].iloc[-1] < platform_high:
                return False

            return True
        except Exception as e:
            # print(f"计算平台突破失败: {e}")
            return False

    def _cal_big_divergence(self):
        """
        [核心优化] 计算大分歧形态.
        严格定义为在高位或低位的转折点，伴随巨量和长影线。
        """
        try:
            if len(self.data) < 60: return False

            day1 = self.data.iloc[-1]
            day2 = self.data.iloc[-2]

            # 1. 计算影线和实体
            def get_k_features(day):
                body = abs(day[self.close_column] - day[self.open_column])
                upper_shadow = day[self.high_column] - max(day[self.close_column], day[self.open_column])
                lower_shadow = min(day[self.close_column], day[self.open_column]) - day[self.low_column]
                total_range = day[self.high_column] - day[self.low_column]
                return body, upper_shadow, lower_shadow, total_range
            
            body1, upper1, lower1, range1 = get_k_features(day1)
            body2, upper2, lower2, range2 = get_k_features(day2)

            if range1 == 0 or range2 == 0: return False

            # 2. 形态定义：一天长上影，一天长下影
            long_upper_ratio, long_lower_ratio = 0.4, 0.4 # 影线占总振幅40%以上
            pattern1 = (upper2 / range2 > long_upper_ratio) and (lower1 / range1 > long_lower_ratio) # 前天上影，今天下影
            pattern2 = (lower2 / range2 > long_lower_ratio) and (upper1 / range1 > long_upper_ratio) # 前天下影，今天上影
            
            if not (pattern1 or pattern2): return False

            # 3. 位置确认：必须发生在阶段性高位或低位
            period_data = self.data.iloc[-60:]
            high_60 = period_data[self.high_column].max()
            low_60 = period_data[self.low_column].min()
            current_high = max(day1[self.high_column], day2[self.high_column])
            current_low = min(day1[self.low_column], day2[self.low_column])
            
            # 计算价格分位
            price_percentile = (current_high - low_60) / (high_60 - low_60)
            is_at_top = price_percentile > 0.85 # 发生在85%以上的高位
            is_at_bottom = price_percentile < 0.15 # 发生在15%以下的低位
            
            if not (is_at_top or is_at_bottom): return False

            # 4. 成交量确认：这两天必须是巨量
            vol_avg_20 = self.data[self.volume_column].iloc[-22:-2].mean()
            vol_day1 = day1[self.volume_column]
            vol_day2 = day2[self.volume_column]
            
            if not (vol_day1 > vol_avg_20 * 2 and vol_day2 > vol_avg_20 * 2): # 两天成交量都是20日均量的2倍以上
                return False

            # 如果是底部大分歧，则是一个潜在买点
            if is_at_bottom:
                return True
            else: # 顶部大分歧是卖点
                return False

        except Exception as e:
            # print(f"计算大分歧失败: {e}")
            return False

    def _cal_fund_accumulation(self):
        """
        [核心优化] 计算资金吸筹指标.
        核心逻辑: 长期底部横盘，波动率持续下降，但OBV（能量潮）指标却在悄悄走高，形成“价平量升”的背离。
        """
        try:
            if len(self.data) < 120 or 'obv' not in self.data.columns: return False
            
            # 1. 寻找长达60-90天的底部盘整区
            period = 90
            data_period = self.data.iloc[-period:]
            
            # 条件a: 价格处于一年内的相对低位
            low_250 = self.data[self.low_column].rolling(250).min().iloc[-1]
            if self.data[self.close_column].iloc[-1] > low_250 * 1.5: # 当前价格不能比年内低点高出50%
                return False

            # 条件b: 近期波动率持续萎缩
            # 布林带宽度是衡量波动率的好指标
            upper, middle, lower = ta.BBANDS(data_period[self.close_column], timeperiod=20)
            bbw = (upper - lower) / middle
            # 要求当前布林带宽度处于90天内的最低1/3水平
            if bbw.iloc[-1] > bbw.quantile(0.33):
                return False
                
            # 2. 核心信号：OBV与价格发生底背离
            # 价格在盘整区内可能创新低或走平，但OBV却要创出盘整区的新高
            price_trend_slope = np.polyfit(np.arange(period), data_period[self.close_column], 1)[0]
            obv_trend_slope = np.polyfit(np.arange(period), data_period['obv'], 1)[0]
            
            # 价格趋势平缓或向下，但OBV趋势明显向上
            if price_trend_slope < 0.05 and obv_trend_slope > 0:
                # 进一步确认OBV斜率显著大于价格斜率
                # 简单比较即可，因为我们已经限定了价格斜率很小
                return True

            return False
        except Exception as e:
            # print(f"计算资金吸筹失败: {e}")
            return False

    def _cal_buy_signal(self, isStrongTrend, isBreakoutPlatform, isBigDivergence, isFundAccumulation):
        """
        [优化] 计算最终买入信号.
        买点逻辑更加清晰:
        - 强势趋势中的回调买点 (isStrongTrend为True)
        - 资金吸筹后的平台突破买点 (isFundAccumulation 和 isBreakoutPlatform 同时或先后出现)
        - 底部大分歧反转买点 (isBigDivergence为True)
        """
        try:
            # 1. 最强的信号: 吸筹后的突破
            # 如果近期（30天内）曾识别出吸筹，现在又出现平台突破，这是最经典的买点
            if isBreakoutPlatform:
                # 简单回溯检查前30天是否有吸筹迹象（此处为简化，实际可做的更复杂）
                # 为了不改变结构，这里我们只判断当下的信号
                if isFundAccumulation: # 如果突破时仍能检测到吸筹特征，信号极强
                    return True
                # 如果不能，但突破本身成立，也是一个强信号
                return True

            # 2. 趋势跟踪买点
            # 处于强势趋势中，是右侧交易者的首选
            if isStrongTrend:
                return True
            
            # 3. 左侧反转买点
            # 识别出底部大分歧，适合愿意承担风险的左侧交易者
            if isBigDivergence:
                return True

            # 将原有的 ">=2" 逻辑作为补充
            conditions_met = sum([isStrongTrend, isBreakoutPlatform, isBigDivergence, isFundAccumulation])
            if conditions_met >= 2:
                return True

            return False
        except Exception as e:
            # print(f"计算买入信号失败: {e}")
            return False