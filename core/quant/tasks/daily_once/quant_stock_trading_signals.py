# -*- coding: UTF-8 -*-
"""
股票买卖点分析工具 (Trading Signals Analyzer)

0 30 19 * * 1-5

主要功能:
1. 多维度技术指标分析
2. 基本面分析评分
3. 风口面分析评分
4. 资金面分析评分
5. 市场面分析评分
6. 风险面分析评分
7. 宏观面分析评分
8. 行为面分析评分
9. 综合加权评分系统
10. 买卖点信号生成
11. 风险控制建议
12. 历史回测验证
13. 实时监控预警


个股、板块、市场、宏观四个层次的分析

分析维度:
- 技术面: MA、MACD、KDJ、RSI、布林带、ATR、CCI等
- 基本面: 估值、财务、成长性、盈利能力
- 风口面: 行业热度、政策支持、市场关注度
- 资金面: 流动性、资金流向、换手率、北向资金
- 综合评分: 多因子加权评分

- 宏观面: 利率、通胀、政策环境
- 市场面: 市场情绪、板块轮动、市场结构
- 风险面: 波动性、流动性风险、系统性风险
- 行为面: 投资者行为、市场微观结构


关于数据：历史行情只有成交数据，其它基本面数据只能使用实时数据


使用方式:
python tools/stock_trading_signals.py -c 000001 -d 20250710
python tools/stock_trading_signals.py -c 000001 -b 20250601 -e 20250710
python tools/stock_trading_signals.py --scan --date 20250710
"""

import sys
import os
from pathlib import Path

# 添加日期处理辅助函数
def get_current_date(date_str=None):
    """
    获取当前日期或验证用户提供的日期
    :param date_str: 可选的日期字符串 (YYYYMMDD)
    :return: 格式化的日期字符串 (YYYYMMDD)
    """
    if date_str:
        try:
            datetime.strptime(date_str, '%Y%m%d')
            return date_str
        except ValueError:
            logger.error(f"❌ 日期格式错误: {date_str}，请使用 YYYYMMDD 格式")
            # 返回当前日期作为备选
            return time.strftime('%Y%m%d', time.localtime())
    return time.strftime('%Y%m%d', time.localtime())

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent  # 回到quant目录
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pandas as pd
import numpy as np
import argparse
import time
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from tasks.tools.data_models import (
    SignalType, RiskLevel, TradingSignal, StockOldAnalysis,
    StockAnalysis, FundamentalAnalysis, SentimentAnalysis,
    LiquidityAnalysis, MarketAnalysis, RiskAnalysis,
    MacroAnalysis, BehavioralAnalysis
)
from enum import Enum
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

# 数据库相关
from db.database import db_manager, DBSession, engine
from db.models import Stock, StockHistory, StockCode, Daliy, StockQuant, QuantList
from sqlalchemy import and_, desc, func
from sqlalchemy.dialects.mysql import insert

# 工具类
from utils.baostock import Baostock
from utils.akshare import Akshare
from utils.helper import ago_day_timestr, sz_or_sh, check_bool, isLimit, cal_price_momentum
from utils.logger import logger
from utils.signal_handler import (
    setup_signal_handler,
    register_cleanup,
    is_exit_requested,
    cleanup_and_exit
)
from utils.performance import monitor_performance
from config.settings import get_settings

# 分析器导入
from tasks.tools.analyzers_init import (
    BehavioralAnalyzer,
    FundamentalAnalyzer,
    RiskAnalyzer,
    MarketAnalyzer,
    MacroAnalyzer,
    SentimentAnalyzer,
    LiquidityAnalyzer,
    TechnicalIndicators
)
from tasks.tools.stock_old_analyzer import StockOldAnalyzer

# 模拟数据管理
from config.mock_data_config import get_mock_data_manager, MockDataSettings, MockDataMode, configure_mock_data

# 优化的评分算法
from algorithms.optimized_scoring_algorithm import OptimizedScoringAlgorithm, ScoringWeights

# 数据获取
from db.fetch import (
    fetch_all_stock, fetch_quant, fetch_quant_raw, quant_monitor,
    fetch_all_kline, fetch_all_history, fetch_quant_stock, is_trade_date,
    fetch_all_stock_data, fetch_all_stock_source
)

# 技术分析库
import talib
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier

# 统计分析库
try:
    import statsmodels.api as sm
except ImportError:
    logger.warning("statsmodels 未安装，CAPM计算将被跳过")
    sm = None

# 优化的分析器
try:
    from .optimized_stock_analyzer import OptimizedStockAnalyzer
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from optimized_stock_analyzer import OptimizedStockAnalyzer
    except ImportError:
        # 如果都失败了，创建一个占位符类
        class OptimizedStockAnalyzer:
            def __init__(self, max_workers=8):
                self.max_workers = max_workers

            def scan_market_signals_optimized(self, date):
                from utils.logger import logger
                logger.warning("优化分析器不可用，请检查导入")
                return []

class FastIndicatorCalculator:
    """快速技术指标计算器"""

    def __init__(self):
        self._cache = {}
        self._cache_limit = 500

    @lru_cache(maxsize=100)
    def calculate_fast_indicators(self, code: str, df_hash: str, close_data: tuple,
                                high_data: tuple, low_data: tuple, volume_data: tuple) -> dict:
        """使用缓存的快速指标计算"""
        try:
            close = np.array(close_data)
            high = np.array(high_data)
            low = np.array(low_data)
            volume = np.array(volume_data)

            indicators = {}

            # 快速MA计算
            if len(close) >= 30:
                ma5 = talib.SMA(close, timeperiod=5)[-1]
                ma10 = talib.SMA(close, timeperiod=10)[-1]
                ma20 = talib.SMA(close, timeperiod=20)[-1]
                ma30 = talib.SMA(close, timeperiod=30)[-1]

                indicators['ma_bullish'] = close[-1] > ma5 > ma10 > ma20 > ma30
                indicators['ma_signal'] = 1 if indicators['ma_bullish'] else -1

            # 快速MACD计算
            if len(close) >= 26:
                dif, dea, hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
                if len(dif) > 1 and not np.isnan(dif[-1]) and not np.isnan(dea[-1]):
                    if dif[-1] > dea[-1] and dif[-2] <= dea[-2]:
                        indicators['macd_signal'] = 1  # 金叉
                    elif dif[-1] < dea[-1] and dif[-2] >= dea[-2]:
                        indicators['macd_signal'] = -1  # 死叉
                    else:
                        indicators['macd_signal'] = 0

            # 快速RSI计算
            if len(close) >= 14:
                rsi = talib.RSI(close, timeperiod=14)[-1]
                if not np.isnan(rsi):
                    if rsi < 30:
                        indicators['rsi_signal'] = 1  # 超卖
                    elif rsi > 70:
                        indicators['rsi_signal'] = -1  # 超买
                    else:
                        indicators['rsi_signal'] = 0

            # 快速成交量分析
            if len(volume) >= 10:
                volume_ma5 = talib.SMA(volume, timeperiod=5)[-1]
                if not np.isnan(volume_ma5) and volume_ma5 > 0:
                    volume_ratio = volume[-1] / volume_ma5
                    if volume_ratio > 2.0:
                        indicators['volume_signal'] = 1  # 放量
                    elif volume_ratio < 0.5:
                        indicators['volume_signal'] = -1  # 缩量
                    else:
                        indicators['volume_signal'] = 0

            return indicators

        except Exception as e:
            logger.error(f"快速指标计算失败: {e}")
            return {}

    def get_df_hash(self, df: pd.DataFrame) -> str:
        """获取DataFrame的简单哈希"""
        try:
            if len(df) > 0:
                return f"{len(df)}_{df.iloc[-1]['close']}_{df.iloc[-1]['volume']}"
            return "empty"
        except:
            return "error"


class BatchProcessor:
    """批量处理器"""

    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or min(mp.cpu_count(), 6)
        self.fast_calculator = FastIndicatorCalculator()

    def process_stocks_batch(self, stock_codes: list, date: str, analyzer) -> list:
        """批量处理股票"""
        try:
            logger.info(f"开始批量处理 {len(stock_codes)} 只股票，使用 {self.max_workers} 个线程")

            # 预加载数据
            start_date = ago_day_timestr(60, '%Y%m%d')
            all_data = fetch_all_history(begin=start_date, end=date)

            if all_data is None:
                logger.warning("没有获取到历史数据")
                return []
            if all_data.empty:
                logger.warning("没有获取到历史数据")
                return []

            # 按股票分组
            stock_data = {}
            for code in stock_codes:
                code_data = all_data[all_data['code'] == code].copy()
                if not code_data.empty and len(code_data) >= 10:
                    code_data = code_data.sort_values('tradedate', ascending=True)
                    stock_data[code] = code_data

            logger.info(f"预加载完成，获取到 {len(stock_data)} 只股票的数据")

            # 分批处理
            batch_size = max(50, len(stock_codes) // self.max_workers)
            batches = [stock_codes[i:i + batch_size] for i in range(0, len(stock_codes), batch_size)]

            results = []

            # 使用线程池
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []

                for batch in batches:
                    future = executor.submit(self._process_batch, batch, stock_data, analyzer)
                    futures.append(future)

                for future in futures:
                    try:
                        batch_results = future.result(timeout=300)
                        results.extend(batch_results)
                    except Exception as e:
                        logger.error(f"批次处理失败: {e}")

            logger.info(f"批量处理完成，成功处理 {len(results)} 只股票")
            return results

        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return []

    def _process_batch(self, stock_codes: list, stock_data: dict, analyzer) -> list:
        """处理单个批次"""
        results = []

        for code in stock_codes:
            try:
                df = stock_data.get(code)
                if df is None:
                    continue
                if df.empty:
                    continue

                # 快速计算指标
                df_hash = self.fast_calculator.get_df_hash(df)
                close_data = tuple(df['close'].astype(float).values)
                high_data = tuple(df['high'].astype(float).values)
                low_data = tuple(df['low'].astype(float).values)
                volume_data = tuple(df['volume'].astype(float).values)

                indicators = self.fast_calculator.calculate_fast_indicators(
                    code, df_hash, close_data, high_data, low_data, volume_data
                )

                # 快速评分
                score = self._calculate_fast_score(code, df, indicators)

                # 同时调用旧分析器来填充 quant_stock_list
                try:
                    if hasattr(analyzer, 'stock_old_analyzer'):
                        old_analysis = analyzer.stock_old_analyzer.analyze_stock(code, df)
                        score['old_analysis'] = old_analysis
                        quant_stock_list.append(old_analysis)
                except Exception as e:
                    logger.debug(f"旧分析器处理股票 {code} 失败: {e}")

                if score:
                    results.append(score)

            except Exception as e:
                logger.warning(f"处理股票 {code} 失败: {e}")
                continue

        return results

    def _calculate_fast_score(self, code: str, df: pd.DataFrame, indicators: dict) -> dict:
        """快速评分计算"""
        try:
            # 技术面评分
            technical_score = 50.0
            technical_score += indicators.get('ma_signal', 0) * 20
            technical_score += indicators.get('macd_signal', 0) * 15
            technical_score += indicators.get('rsi_signal', 0) * 10
            technical_score += indicators.get('volume_signal', 0) * 5

            # 其他维度简化评分
            fundamental_score = 50.0
            sentiment_score = 50.0 + (5 if code.startswith('30') else 0)  # 创业板加分

            # 资金面评分
            liquidity_score = 50.0
            if len(df) >= 10:
                recent_volume = df['volume'].iloc[-5:].mean()
                avg_volume = df['volume'].iloc[-20:].mean()
                if avg_volume > 0:
                    volume_ratio = recent_volume / avg_volume
                    if volume_ratio > 1.5:
                        liquidity_score += 15
                    elif volume_ratio > 1.2:
                        liquidity_score += 10

            # 综合评分
            comprehensive_score = (
                technical_score * 0.6 +
                fundamental_score * 0.0 +
                sentiment_score * 0.2 +
                liquidity_score * 0.2
            )

            return {
                'code': code,
                'technical_score': max(0, min(100, technical_score)),
                'fundamental_score': fundamental_score,
                'sentiment_score': max(0, min(100, sentiment_score)),
                'liquidity_score': max(0, min(100, liquidity_score)),
                'comprehensive_score': max(0, min(100, comprehensive_score)),
                'current_price': float(df.iloc[-1]['close']),
                'last_update': df.iloc[-1]['tradedate']
            }

        except Exception as e:
            logger.error(f"快速评分计算失败: {e}")
            return None




# 计算量化录入收益
def cal_quant_rate(df, df_quant):
    """
    计算量化录入收益

    :param df: 股票数据DataFrame
    :param df_quant: 量化数据DataFrame
    """
    try:
        if df_quant is None or df is None:
            return
        if df_quant.empty or df.empty:
            return

        for idex, q in df_quant.iterrows():
            percent = 0
            daylast = df.iloc[-1]

            # 查找交易日数据
            if 'tradedate' in df.columns:
                daytrade = df[df['tradedate'] == q.date]
            else:
                # 如果没有tradedate字段，使用date字段
                daytrade = df[df['date'] == q.date]

            if not daytrade.empty and daylast.close > 0:
                day1 = daytrade.iloc[0]
                if day1.close > 0:
                    percent = round((daylast.close - day1.close) / day1.close, 4)

                    with db_manager.get_session() as session:
                        new_stock = StockQuant(
                            code=q.code,
                            date=q.date,
                            rate=float(percent)
                        )
                        session.merge(new_stock)
                        session.commit()

    except Exception as e:
        logger.error(f"计算量化收益失败: {e}")

def save_sql(name, desc, arr):
    """
    保存量化选股结果到数据库

    :param name: 策略名称
    :param desc: 策略描述
    :param arr: 股票代码列表
    """
    try:
        if not arr:
            logger.warning(f"策略 {name} 没有选出股票")
            return

        logger.info(f"保存策略 {name}: {len(arr)} 只股票")

        with db_manager.get_session() as session:
            # 保存到QuantList表
            item = {
                'name': name,
                'desc': desc,
                'list': (',').join(arr)
            }
            insert_stmt = insert(QuantList).values(**item)
            on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
            session.execute(on_duplicate_key_stmt)

            # 使用与市场扫描模式一致的日期生成方式
            today = get_current_date()

            # 确保交易日录入
            if is_trade_date(today):
                # 批量插入StockQuant记录
                stock_quant_records = []
                for stock in arr:
                    stock_quant_records.append({
                        'code': stock,
                        'reason': name,
                        'date': today
                    })

                # 批量插入
                if stock_quant_records:
                    for record in stock_quant_records:
                        insert_stmt = insert(StockQuant).values(**record)
                        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**record)
                        session.execute(on_duplicate_key_stmt)

            session.commit()

    except Exception as e:
        logger.error(f"保存策略 {name} 失败: {e}")


# 全局变量声明
bs = None
session = None
stocks = None
klines = None
start_date = None
end_date = None
decision_date = None
average_amount = None
average_value = None
quantId = None
quantRaw = None
hs300 = None
sz50 = None
cy50 = None
zz500 = None
kc50 = None
ret_max = None
df_all = None
df_quant_stock = None
df_source = None
df_stock = None
stock_trend_dict = {}
quant_stock_list = []

@monitor_performance
def initialize_system():
    """
    初始化量化分析系统

    :return: 是否成功
    """
    global bs, session, stocks, klines, start_date, end_date, decision_date
    global average_amount, average_value, quantId, quantRaw, hs300, sz50, cy50
    global zz500, kc50, ret_max, df_all, df_quant_stock, df_source, df_stock

    try:
        logger.info("开始初始化量化分析系统...")

        # 初始化基础组件
        bs = Baostock()
        session = DBSession()

        # 获取基础配置
        klines = fetch_all_kline()
        start_date, end_date, decision_date, average_amount, average_value, quantId = fetch_quant()
        quantRaw = fetch_quant_raw()

        logger.info(f'统计时间：{start_date} - {end_date}, 决断时间点：{decision_date}')

        # 获取指数数据
        logger.info("加载指数数据...")

        def safe_get_index_data(index_code, index_name):
            """安全获取指数数据"""
            try:
                data = bs.query_history_data(index_code, start_date=start_date, end_date=end_date)
                if data is None:
                    logger.warning(f"指数 {index_name} ({index_code}) 无历史数据")
                    return pd.DataFrame(columns=['date', 'close'])
                if data.empty:
                    logger.warning(f"指数 {index_name} ({index_code}) 无历史数据")
                    return pd.DataFrame(columns=['date', 'close'])

                if 'date' not in data.columns or 'close' not in data.columns:
                    logger.warning(f"指数 {index_name} ({index_code}) 数据格式异常: {data.columns.tolist()}")
                    return pd.DataFrame(columns=['date', 'close'])

                result = data[['date','close']].set_index('date').sort_index().reset_index()
                logger.debug(f"成功加载指数 {index_name}: {len(result)} 条记录")
                return result

            except Exception as e:
                logger.error(f"获取指数 {index_name} ({index_code}) 数据失败: {e}")
                return pd.DataFrame(columns=['date', 'close'])

        # 沪深300
        hs300 = safe_get_index_data('sh.000300', '沪深300')

        # 上证50
        sz50 = safe_get_index_data('sh.000016', '上证50')

        # 创业板50
        cy50 = safe_get_index_data('sz.399673', '创业板50')

        # 中证500
        zz500 = safe_get_index_data('sh.000905', '中证500')

        # 科创50
        kc50 = safe_get_index_data('sh.000688', '科创50')

        # 计算动量基准
        index_momentums = [quantRaw.momentumRet]

        # 安全计算各指数动量
        index_data_list = [
            (hs300, '沪深300'),
            (sz50, '上证50'),
            (cy50, '创业板50'),
            (zz500, '中证500'),
            (kc50, '科创50')
        ]

        for index_data, index_name in index_data_list:
            try:
                if not index_data.empty and len(index_data) > quantRaw.momentumDay:
                    momentum = cal_price_momentum(index_data, quantRaw.momentumDay)
                    index_momentums.append(momentum)
                    logger.debug(f"{index_name} 动量: {momentum}")
                else:
                    logger.warning(f"{index_name} 数据不足，跳过动量计算")
            except Exception as e:
                logger.warning(f"计算 {index_name} 动量失败: {e}")

        ret_max = max(index_momentums) if index_momentums else 0

        logger.info(f"动量基准设定为: {ret_max}")


        # 获取股票趋势热度数据
        try:
            sql = 'SELECT SUM(score) as score, code FROM stocktrend GROUP BY code LIMIT 1000'
            with engine.connect() as conn:
                result = conn.execute(sql)
                for row in result:
                    stock_trend_dict[row['code']] = row['score']
            logger.info(f"加载股票热度数据: {len(stock_trend_dict)} 只股票")
        except Exception as e:
            logger.warning(f"加载股票热度数据失败: {e}")

        # 获取股票数据
        logger.info("加载股票历史数据...")
        df_all = fetch_all_history(begin=start_date, end=end_date)
        df_quant_stock = fetch_quant_stock()
        df_source = fetch_all_stock_source()
        df_stock = fetch_all_stock_data()

        logger.info("✅ 系统初始化完成")
        return True

    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False

class WinRateTracker:
    """胜率跟踪器，用于记录和分析交易信号的胜率"""
    def __init__(self):
        self.signal_records = []  # 存储信号记录
        self.min_history_required = 20  # 计算统计所需的最小历史记录数
        self.win_threshold = 0.02  # 定义获胜的最小涨幅(2%)
        
    def record_signal(self, stock_code: str, signal_type: Enum, signal_date: str, 
                     entry_price: float, stop_loss: float, take_profit: float):
        """记录交易信号"""
        record = {
            'stock_code': stock_code,
            'signal_type': signal_type,
            'signal_date': signal_date,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'exit_date': None,
            'exit_price': None,
            'result': None  # None: 未平仓, True: 盈利, False: 亏损
        }
        self.signal_records.append(record)
        logger.debug(f"记录信号: {stock_code} {signal_type} {signal_date}")
        
    def update_signal_result(self, stock_code: str, signal_date: str, exit_date: str, exit_price: float):
        """更新信号结果"""
        for record in self.signal_records:
            if record['stock_code'] == stock_code and record['signal_date'] == signal_date and record['exit_date'] is None:
                record['exit_date'] = exit_date
                record['exit_price'] = exit_price
                
                # 计算结果
                if record['signal_type'].name in ['STRONG_BUY', 'BUY']:
                    # 买入信号: 退出价格高于入场价格+阈值为盈利
                    profit = (exit_price - record['entry_price']) / record['entry_price']
                    record['result'] = profit >= self.win_threshold
                elif record['signal_type'].name in ['STRONG_SELL', 'SELL']:
                    # 卖出信号: 退出价格低于入场价格-阈值为盈利
                    profit = (record['entry_price'] - exit_price) / record['entry_price']
                    record['result'] = profit >= self.win_threshold
                
                logger.debug(f"更新信号结果: {stock_code} {signal_date} 结果: {record['result']}")
                return True
        return False
        
    def calculate_win_rate(self, signal_type: Enum = None) -> float:
        """计算胜率"""
        if len(self.signal_records) < self.min_history_required:
            return 0.5  # 历史记录不足时返回中性值
        
        # 筛选已平仓且有结果的记录
        closed_records = [r for r in self.signal_records if r['result'] is not None]
        
        # 按信号类型筛选
        if signal_type:
            closed_records = [r for r in closed_records if r['signal_type'] == signal_type]
        
        if not closed_records:
            return 0.5  # 没有符合条件的记录时返回中性值
        
        # 计算胜率
        win_count = sum(1 for r in closed_records if r['result'])
        win_rate = win_count / len(closed_records)
        logger.debug(f"计算胜率: {win_rate:.2%} ({win_count}/{len(closed_records)})")
        return win_rate
        
    def get_signal_effectiveness(self) -> Dict[str, float]:
        """获取不同信号类型的有效性"""
        effectiveness = {}
        for signal_type in SignalType:
            effectiveness[signal_type.name] = self.calculate_win_rate(signal_type)
        return effectiveness

    def get_open_signals(self, stock_code: Optional[str] = None) -> list:
        """获取未平仓的交易信号"""
        # 筛选未平仓的信号
        open_signals = [r for r in self.signal_records if r['exit_date'] is None]
        
        # 如果指定了股票代码，则进一步筛选
        if stock_code:
            open_signals = [r for r in open_signals if r['stock_code'] == stock_code]
            
        return open_signals
        
    def get_win_rate_enhancement_suggestions(self) -> List[str]:
        """基于胜率分析提供增强建议"""
        suggestions = []
        effectiveness = self.get_signal_effectiveness()
        
        # 分析各类信号的有效性
        for signal_type, rate in effectiveness.items():
            if rate > 0.65:
                suggestions.append(f"{signal_type}信号表现优异(胜率{rate:.2%})，可考虑增加该类型信号的权重")
            elif rate < 0.45:
                suggestions.append(f"{signal_type}信号表现欠佳(胜率{rate:.2%})，建议降低该类型信号的权重或优化其生成条件")
        
        # 整体胜率分析
        overall_rate = self.calculate_win_rate()
        if overall_rate > 0.6:
            suggestions.append(f"整体胜率良好({overall_rate:.2%})，可考虑微调参数以进一步提高")
        elif overall_rate < 0.5:
            suggestions.append(f"整体胜率较低({overall_rate:.2%})，建议重新评估信号生成逻辑")
        
        return suggestions

class TradingSignalAnalyzer:
    """交易信号分析器"""
    
    def __init__(self):
        self.stock_old_analyzer = StockOldAnalyzer(None)
        self.indicators = TechnicalIndicators()
        self.fundamental_analyzer = FundamentalAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.liquidity_analyzer = LiquidityAnalyzer()
        self.market_analyzer = MarketAnalyzer()
        self.macro_analyzer = MacroAnalyzer()
        self.behavioral_analyzer = BehavioralAnalyzer()
        self.risk_analyzer = RiskAnalyzer()  # 新增风险面分析器
        self.settings = get_settings()

        # 综合评分权重配置（只用四个维度）
        self.score_weights = {
            'technical': quantRaw.score2Weight,      # 技术面权重60%
            'fundamental': quantRaw.score1Weight,    # 基本面权重0%
            'sentiment': quantRaw.score4Weight,      # 风口面权重30%
            'liquidity': quantRaw.score3Weight,      # 资金面权重10%
        }

        # 初始化胜率统计器
        self.win_rate_tracker = WinRateTracker()

        # 动态权重调整因子
        self.dynamic_weights = {
            'trend': 1.0,  # 趋势指标权重因子
            'momentum': 1.0,  # 动量指标权重因子
            'volume': 1.0,  # 成交量指标权重因子
            'volatility': 1.0  # 波动率指标权重因子
        }

        # 初始化优化的评分算法
        scoring_weights = ScoringWeights(
            technical=quantRaw.score2Weight,
            fundamental=quantRaw.score1Weight,
            sentiment=quantRaw.score4Weight,
            liquidity=quantRaw.score3Weight
        )
        self.optimized_scorer = OptimizedScoringAlgorithm(scoring_weights)

        # 行情参考缓存
        self.market_context = None

        # 评分算法版本标识
        self.use_optimized_scoring = True  # 是否使用优化的评分算法

        # 批量处理器
        self.batch_processor = BatchProcessor(max_workers=6)

    def analyze_market_context(self, date=None):
        """只计算一次行情参考"""
        # code参数可为None或主板指数
        self.market_context = {
            'market': self.market_analyzer.analyze_market(None),
            'risk': self.risk_analyzer.analyze_risk(None),  # 新增风险面分析
            'macro': self.macro_analyzer.analyze_macro(None),
            'behavioral': self.behavioral_analyzer.analyze_behavioral(None),
        }

    def analyze_stock(self, code: str, start_date: str = None, 
                     end_date: str = None) -> Optional[StockAnalysis]:
        """分析单只股票（只用四个维度参与评分）"""
        try:
            logger.info(f"开始分析股票: {code}")
            
            # 获取股票数据
            df = self._get_stock_data(code, start_date, end_date)
            if df is None:
                logger.warning(f"股票 {code} 没有数据")
                return None
            if df.empty:
                logger.warning(f"股票 {code} 没有数据")
                return None
            
            # 获取股票名称
            stock_name = self._get_stock_name(code)

            old_analysis = self.stock_old_analyzer.analyze_stock(code, df)
            if old_analysis is None:
                logger.debug(f"股票 {code} 旧分析器返回None")
            
            # 计算技术指标
            signals = self._calculate_all_signals(df)
            
            # 生成交易信号
            trading_signals = self._generate_trading_signals(df, signals)
            
            # 计算技术面评分
            technical_score = self._calculate_technical_score(signals)
            
            # 分析基本面
            fundamental_analysis = self.fundamental_analyzer.analyze_fundamentals(code)
            fundamental_score = fundamental_analysis.overall_score if fundamental_analysis else 50.0
            
            # 分析风口面
            sentiment_analysis = self.sentiment_analyzer.analyze_sentiment(code)
            sentiment_score = sentiment_analysis.overall_score if sentiment_analysis else 50.0
            
            # 分析资金面
            liquidity_analysis = self.liquidity_analyzer.analyze_liquidity(code)
            liquidity_score = liquidity_analysis.overall_score if liquidity_analysis else 50.0
            
            # 只保留四个维度参与评分
            comprehensive_score = self._calculate_comprehensive_score(
                technical_score, fundamental_score, sentiment_score, liquidity_score
            )
            
            # 生成投资建议
            recommendation = self._generate_comprehensive_recommendation(
                trading_signals, technical_score, fundamental_score, 
                sentiment_score, liquidity_score, comprehensive_score
            )
            
            current_price = df.iloc[-1]['close']
            last_update = df.iloc[-1]['tradedate']
            
            return StockAnalysis(
                code=code,
                name=stock_name,
                current_price=current_price,
                signals=trading_signals,
                technical_score=technical_score,
                fundamental_score=fundamental_score,
                sentiment_score=sentiment_score,
                liquidity_score=liquidity_score,
                market_score=0.0,
                risk_score=0.0,
                macro_score=0.0,
                behavioral_score=0.0,
                comprehensive_score=comprehensive_score,
                risk_level=self._calculate_risk_level(df, signals),
                recommendation=recommendation,
                last_update=last_update,
                old_analysis=old_analysis
            )
            
        except Exception as e:
            logger.error(f"分析股票 {code} 失败: {e}")
            return None

    def scan_market_signals(self, date: str = None, use_optimized: bool = True) -> List[StockAnalysis]:
        """扫描市场信号，支持优化模式"""
        try:
            if not date:
                date = time.strftime('%Y%m%d', time.localtime())

            # 如果使用优化模式，使用批量处理
            if use_optimized:
                logger.info(f"使用优化模式扫描市场信号，日期: {date}")

                # 检查交易日
                if not is_trade_date(date):
                    logger.warning(f"输入日期 {date} 不是交易日")
                    return []

                # 获取股票列表
                stock_codes = fetch_all_stock(mark=False)
                if not stock_codes:
                    logger.error("没有找到股票代码")
                    return []

                # 批量处理
                batch_results = self.batch_processor.process_stocks_batch(stock_codes, date, self)

                # 批量保存到数据库
                self._batch_save_results(batch_results)

                # 转换为 StockAnalysis 格式
                results = []
                for result in batch_results:
                    # 基于评分生成简单的交易信号
                    signals = self._generate_signals_from_scores(result)

                    analysis = type('StockAnalysis', (), {
                        'code': result['code'],
                        'name': '',
                        'current_price': result['current_price'],
                        'technical_score': result['technical_score'],
                        'fundamental_score': result['fundamental_score'],
                        'sentiment_score': result['sentiment_score'],
                        'liquidity_score': result['liquidity_score'],
                        'comprehensive_score': result['comprehensive_score'],
                        'last_update': result['last_update'],
                        'signals': signals,
                        'old_analysis': None,  
                        'recommendation': self._generate_recommendation_from_score(result['comprehensive_score'])
                    })()
                    results.append(analysis)

                # 按综合评分排序
                results.sort(key=lambda x: x.comprehensive_score, reverse=True)

                logger.info(f"优化模式扫描完成，处理了 {len(results)} 只股票")
                return results

            # 原始模式
            logger.info(f"使用原始模式扫描市场信号，日期: {date}")
            # 检查输入日期是否为交易日
            if not is_trade_date(date):
                logger.warning(f"输入日期 {date} 不是交易日")
                latest_trade_date = self._get_latest_trade_date()
                if latest_trade_date:
                    logger.info(f"自动调整到最近交易日: {latest_trade_date}")
                    date = latest_trade_date
                else:
                    logger.error("无法获取最近交易日，退出扫描")
                    return []
            if not self._validate_trading_data_availability(date):
                logger.warning(f"日期 {date} 的历史数据可能不完整，继续扫描但可能影响分析质量")
            stock_codes = fetch_all_stock(mark=False)
            if not stock_codes:
                logger.error("没有找到股票代码")
                return []

            logger.info(f"获取到 {len(stock_codes)} 只股票代码，开始分析...")
            results = []
            skipped_count = 0
            data_missing_count = 0
            analysis_error_count = 0

            # 先全局计算行情参考
            self.analyze_market_context(date)
            for i, code in enumerate(stock_codes):
                try:
                    # 每处理100只股票输出一次进度
                    if i % 100 == 0:
                        logger.info(f"处理进度: {i}/{len(stock_codes)} ({i/len(stock_codes)*100:.1f}%)")

                    if not self._check_stock_data_availability(code, date):
                        data_missing_count += 1
                        logger.debug(f"股票 {code} 数据缺失")
                        continue

                    analysis = self.analyze_stock(code,
                                                start_date=ago_day_timestr(60, '%Y%m%d'),
                                                end_date=date)

                    if analysis is None:
                        analysis_error_count += 1
                        logger.debug(f"股票 {code} 分析失败，返回None")
                        continue

                    with db_manager.get_session() as session:
                        old_stock = session.query(Stock).filter(Stock.code == code).first()

                        # 存入数据库
                        if analysis:
                            new_stock = Stock(
                                code=code,
                                lastscore=getattr(old_stock, 'score', 0),
                                score=analysis.comprehensive_score,
                                score1=analysis.fundamental_score,
                                score2=analysis.technical_score,
                                score3=analysis.liquidity_score,
                                score4=analysis.sentiment_score,
                                # 兼容旧数据
                                limit=analysis.old_analysis.limit,
                                isBottomInversion=analysis.old_analysis.isBottomInversion,
                                isHeavyVolume=analysis.old_analysis.isHeavyVolume,
                                isTup=analysis.old_analysis.isTup,
                                ret=analysis.old_analysis.ret,
                                md=analysis.old_analysis.md,
                                alpha=analysis.old_analysis.alpha,
                                beta=analysis.old_analysis.beta,
                                momentum=analysis.old_analysis.momentum,
                                decisionPercent=analysis.old_analysis.decisionPercent,
                                ret10=analysis.old_analysis.ret10,
                                ret20=analysis.old_analysis.ret20,
                                ret100=analysis.old_analysis.ret100,
                                kline=analysis.old_analysis.kline,
                                emv=analysis.old_analysis.emv,
                                macd=analysis.old_analysis.macd,
                                ma=analysis.old_analysis.ma,
                                max30=analysis.old_analysis.max30,
                            )
                            session.merge(new_stock)
                            session.commit()

                    if analysis and analysis.signals:
                        results.append(analysis)
                        logger.debug(f"股票 {code} 分析成功，有信号")
                    else:
                        skipped_count += 1
                        logger.debug(f"股票 {code} 分析成功但无信号")
                except Exception as e:
                    logger.warning(f"分析股票 {code} 失败: {e}")
                    analysis_error_count += 1
                    continue
            results.sort(key=lambda x: x.technical_score, reverse=True)
            logger.info(f"市场扫描完成:")
            logger.info(f"  - 总股票数: {len(stock_codes)}")
            logger.info(f"  - 找到 {len(results)} 只有信号的股票")
            logger.info(f"  - 跳过 {skipped_count} 只股票（无信号）")
            logger.info(f"  - 分析失败 {analysis_error_count} 只股票")
            logger.info(f"  - 数据缺失 {data_missing_count} 只股票")

            return results
        except Exception as e:
            logger.error(f"市场扫描失败: {e}")
            return []

    def _generate_signals_from_scores(self, result: dict) -> List[TradingSignal]:
        """基于评分生成简单的交易信号"""
        try:
            signals = []
            comprehensive_score = result['comprehensive_score']
            technical_score = result['technical_score']
            current_price = result['current_price']
            last_update = result['last_update']

            # 基于综合评分和技术评分生成信号
            if comprehensive_score >= 75 and technical_score >= 70:
                signal_type = SignalType.STRONG_BUY
                strength = min(comprehensive_score, 100)
                confidence = min(technical_score / 100.0, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，强烈买入信号"
            elif comprehensive_score >= 65 and technical_score >= 60:
                signal_type = SignalType.BUY
                strength = min(comprehensive_score * 0.8, 100)
                confidence = min(technical_score / 100.0 * 0.8, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，买入信号"
            elif comprehensive_score <= 25 and technical_score <= 30:
                signal_type = SignalType.STRONG_SELL
                strength = min(100 - comprehensive_score, 100)
                confidence = min((100 - technical_score) / 100.0, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，强烈卖出信号"
            elif comprehensive_score <= 35 and technical_score <= 40:
                signal_type = SignalType.SELL
                strength = min((100 - comprehensive_score) * 0.8, 100)
                confidence = min((100 - technical_score) / 100.0 * 0.8, 1.0)
                reason = f"综合评分{comprehensive_score:.1f}，技术评分{technical_score:.1f}，卖出信号"
            else:
                # 不生成HOLD信号，因为过滤逻辑会跳过它们
                return []

            # 创建交易信号
            trading_signal = TradingSignal(
                date=last_update,
                signal_type=signal_type,
                confidence=confidence,
                strength=strength,
                price=current_price,
                volume=0,  # 优化模式下没有成交量数据
                indicators={},
                risk_level=self._calculate_risk_level_from_score(comprehensive_score),
                reason=reason,
                stop_loss=None,
                take_profit=None
            )
            signals.append(trading_signal)

            return signals

        except Exception as e:
            logger.error(f"基于评分生成信号失败: {e}")
            return []

    def _generate_recommendation_from_score(self, comprehensive_score: float) -> str:
        """基于综合评分生成建议"""
        if comprehensive_score >= 80:
            return "STRONG_BUY - 强烈买入"
        elif comprehensive_score >= 65:
            return "BUY - 买入"
        elif comprehensive_score >= 50:
            return "HOLD - 观望"
        elif comprehensive_score >= 35:
            return "SELL - 卖出"
        else:
            return "STRONG_SELL - 强烈卖出"

    def _calculate_risk_level_from_score(self, comprehensive_score: float) -> RiskLevel:
        """基于综合评分计算风险等级"""
        if comprehensive_score >= 75:
            return RiskLevel.LOW
        elif comprehensive_score >= 60:
            return RiskLevel.MEDIUM
        elif comprehensive_score >= 40:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH

    def _calculate_comprehensive_score(self, technical_score: float, 
                                     fundamental_score: float, 
                                     sentiment_score: float,
                                     liquidity_score: float) -> float:
        """只用四个维度加权"""
        try:
            comprehensive_score = (
                technical_score * self.score_weights['technical'] +
                fundamental_score * self.score_weights['fundamental'] +
                sentiment_score * self.score_weights['sentiment'] +
                liquidity_score * self.score_weights['liquidity']
            )
            return round(comprehensive_score, 2)
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return 50.0

    def _generate_comprehensive_recommendation(self, signals: List[TradingSignal], 
                                             technical_score: float,
                                             fundamental_score: float,
                                             sentiment_score: float,
                                             liquidity_score: float,
                                             comprehensive_score: float) -> str:
        """只用四个维度生成建议"""
        # 可根据实际业务逻辑调整
        if comprehensive_score >= 80:
            return "STRONG_BUY (强烈买入)"
        elif comprehensive_score >= 65:
            return "BUY (买入)"
        elif comprehensive_score >= 50:
            return "HOLD (观望)"
        elif comprehensive_score >= 35:
            return "SELL (卖出)"
        else:
            return "STRONG_SELL (强烈卖出)"
    
    def _get_stock_data(self, code: str, start_date: str = None,
                       end_date: str = None) -> Optional[pd.DataFrame]:
        """获取股票数据"""
        try:
            if not start_date:
                start_date = ago_day_timestr(120, '%Y%m%d')
            if not end_date:
                end_date = time.strftime('%Y%m%d', time.localtime())

            with db_manager.get_session() as session:
                # 使用安全字段列表，避免查询不存在的字段
                # 基于模型分析结果，只使用确认存在的字段
                safe_query = session.query(
                    StockHistory.id,
                    StockHistory.code,
                    StockHistory.date,
                    StockHistory.tradedate,
                    StockHistory.open,
                    StockHistory.high,
                    StockHistory.low,
                    StockHistory.close,
                    StockHistory.volume,
                    StockHistory.amount,
                    StockHistory.turn,
                    StockHistory.pctChg
                ).filter(
                    and_(
                        StockHistory.code == code,
                        StockHistory.tradedate >= start_date,
                        StockHistory.tradedate <= end_date
                    )
                ).order_by(StockHistory.tradedate.asc())

                # 尝试添加扩展字段（如果存在的话）
                try:
                    # 先测试是否可以查询扩展字段
                    extended_query = session.query(
                        StockHistory.id,
                        StockHistory.code,
                        StockHistory.date,
                        StockHistory.tradedate,
                        StockHistory.open,
                        StockHistory.high,
                        StockHistory.low,
                        StockHistory.close,
                        StockHistory.volume,
                        StockHistory.amount,
                        StockHistory.turn,
                        StockHistory.pctChg,
                        StockHistory.ret30,
                        StockHistory.max,
                        StockHistory.min,
                        StockHistory.rose3,
                        StockHistory.fall3,
                        StockHistory.peTTM,
                        StockHistory.psTTM,
                        StockHistory.pcfNcfTTM,
                        StockHistory.pbMRQ
                    ).filter(
                        and_(
                            StockHistory.code == code,
                            StockHistory.tradedate >= start_date,
                            StockHistory.tradedate <= end_date
                        )
                    ).order_by(StockHistory.tradedate.asc()).limit(1)

                    # 测试查询是否成功
                    test_result = extended_query.first()
                    if test_result is not None:
                        # 扩展字段存在，使用完整查询
                        query = extended_query.limit(None)
                    else:
                        # 使用安全查询
                        query = safe_query

                except Exception as e:
                    logger.warning(f"扩展字段查询失败，使用安全字段: {e}")
                    query = safe_query

                df = pd.read_sql(query.statement, session.bind)

                if df.empty:
                    logger.warning(f"股票 {code} 在时间段 {start_date}-{end_date} 没有数据")
                    return None
                return df

        except Exception as e:
            logger.error(f"获取股票 {code} 数据失败: {e}")
            return None
    
    def _get_stock_name(self, code: str) -> str:
        """获取股票名称"""
        try:
            with db_manager.get_session() as session:
                stock = session.query(StockCode).filter(StockCode.code == code).first()
                return stock.name if stock else code
        except Exception as e:
            logger.error(f"获取股票 {code} 名称失败: {e}")
            return code
    
    def _get_latest_trade_date(self) -> Optional[str]:
        """获取小于今天的最近交易日"""
        try:
            # 获取今天的日期
            today = datetime.now().strftime('%Y%m%d')
            
            with db_manager.get_session() as session:
                result = (session.query(Daliy.date)
                         .filter(and_(
                             Daliy.isOpen == True,
                             Daliy.date <= today
                         ))
                         .order_by(desc(Daliy.date))
                         .first())
                return result.date if result else None
        except Exception as e:
            logger.error(f"获取最新交易日失败: {e}")
            return None
    
    def _validate_trading_data_availability(self, target_date: str) -> bool:
        """验证目标日期的交易数据可用性"""
        try:
            # 检查目标日期是否有足够的股票数据
            with db_manager.get_session() as session:
                # 统计目标日期的股票数据数量
                stock_count = (session.query(StockHistory)
                              .filter(StockHistory.tradedate == target_date)
                              .count())
                
                # 获取总股票数量作为参考
                total_stocks = session.query(Stock).count()
                
                # 如果数据覆盖率低于50%，认为数据不完整
                coverage_ratio = stock_count / total_stocks if total_stocks > 0 else 0
                
                logger.info(f"日期 {target_date} 数据覆盖率: {coverage_ratio:.1%} ({stock_count}/{total_stocks})")
                
                return coverage_ratio >= 0.5
                
        except Exception as e:
            logger.error(f"验证交易数据可用性失败: {e}")
            return False
    
    def _check_stock_data_availability(self, code: str, target_date: str) -> bool:
        """检查单只股票的数据可用性"""
        try:
            # 检查目标日期是否有该股票的数据
            with db_manager.get_session() as session:
                # 明确指定查询字段，避免查询不存在的字段
                stock_data = (session.query(
                                 StockHistory.id,
                                 StockHistory.code,
                                 StockHistory.tradedate
                             )
                             .filter(and_(
                                 StockHistory.code == code,
                                 StockHistory.tradedate == target_date
                             ))
                             .first())

                if not stock_data:
                    return False

                # 检查是否有足够的历史数据（至少60个交易日）
                start_date = ago_day_timestr(120, '%Y%m%d')  # 获取更多历史数据
                historical_data = (session.query(StockHistory.id)
                                 .filter(and_(
                                     StockHistory.code == code,
                                     StockHistory.tradedate >= start_date,
                                     StockHistory.tradedate <= target_date
                                 ))
                                 .count())

                # 至少需要60个交易日的数据
                return historical_data >= 60

        except Exception as e:
            logger.error(f"检查股票 {code} 数据可用性失败: {e}")
            return False
    
    def _calculate_all_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算所有技术指标信号"""
        signals = {}
        
        # 计算各类技术指标
        signals.update(self.indicators.calculate_ma_signals(df))
        signals.update(self.indicators.calculate_macd_signals(df))
        signals.update(self.indicators.calculate_rsi_signals(df))
        signals.update(self.indicators.calculate_volume_indicators(df))  # 更新为新的成交量指标
        signals.update(self.indicators.calculate_kdj_signals(df))
        signals.update(self.indicators.calculate_support_resistance(df))
        
        # 新增技术指标
        signals.update(self.indicators.calculate_bollinger_bands(df))
        signals.update(self.indicators.calculate_atr_signals(df))
        signals.update(self.indicators.calculate_cci_signals(df))
        signals.update(self.indicators.calculate_williams_r(df))
        signals.update(self.indicators.calculate_stochastic_signals(df))
        signals.update(self.indicators.calculate_adx_signals(df))
        signals.update(self.indicators.calculate_momentum_indicators(df))
        
        return signals

    def calculate_optimized_stock_score(self, code: str, df: pd.DataFrame) -> Dict[str, Any]:
        """
        使用优化算法计算股票评分
        保持权重不变，但改进评分逻辑
        """
        try:
            # 计算技术指标信号
            signals = self._calculate_all_signals(df)

            # 获取各维度数据
            fundamental_data = self.fundamental_analyzer._get_fundamental_data(code)
            sentiment_data = self.sentiment_analyzer._get_sentiment_data(code)
            liquidity_data = self.liquidity_analyzer._get_liquidity_data(code)

            # 使用优化算法计算各维度评分
            technical_score = self.optimized_scorer.calculate_technical_score(signals)
            fundamental_score = 50.0  # 基本面权重为0，固定50分
            sentiment_score = self.optimized_scorer.calculate_sentiment_score(sentiment_data)
            liquidity_score = self.optimized_scorer.calculate_liquidity_score(liquidity_data)

            # 计算综合评分
            comprehensive_score = self.optimized_scorer.calculate_comprehensive_score(
                technical_score, fundamental_score, sentiment_score, liquidity_score
            )

            # 获取详细评分分解
            score_breakdown = self.optimized_scorer.get_score_breakdown(
                technical_score, fundamental_score, sentiment_score, liquidity_score
            )

            logger.debug(f"股票 {code} 优化评分: 技术面={technical_score:.1f}, "
                        f"风口面={sentiment_score:.1f}, 资金面={liquidity_score:.1f}, "
                        f"综合={comprehensive_score:.1f}")

            return {
                'code': code,
                'technical_score': technical_score,
                'fundamental_score': fundamental_score,
                'sentiment_score': sentiment_score,
                'liquidity_score': liquidity_score,
                'comprehensive_score': comprehensive_score,
                'score_breakdown': score_breakdown,
                'algorithm_version': 'optimized_v1.0'
            }

        except Exception as e:
            logger.error(f"计算股票 {code} 优化评分失败: {e}")
            return {
                'code': code,
                'technical_score': 50.0,
                'fundamental_score': 50.0,
                'sentiment_score': 50.0,
                'liquidity_score': 50.0,
                'comprehensive_score': 50.0,
                'error': str(e)
            }

    def _generate_trading_signals(self, df: pd.DataFrame,
                                signals: Dict[str, Any]) -> List[TradingSignal]:
        """生成交易信号（只输出极强信号）"""
        trading_signals = []
        try:
            current = df.iloc[-1]
            current_price = current['close']
            current_volume = current['volume']
            current_date = current['tradedate']
            # 综合信号强度计算
            signal_strength = 0
            strength_details = {}

            # 动态调整权重
            self._adjust_dynamic_weights(df, signals)
            reasons = []
            # 均线信号
            if signals.get('ma_bullish'):
                ma_contribution = 20
                signal_strength += ma_contribution
                strength_details['ma_bullish'] = ma_contribution
                reasons.append("均线多头排列")
            elif signals.get('ma_bullish') is False:
                ma_contribution = -20
                signal_strength += ma_contribution
                strength_details['ma_bearish'] = ma_contribution
                reasons.append("均线空头排列")
            
            if signals.get('golden_cross'):
                cross_contribution = 15
                signal_strength += cross_contribution
                strength_details['golden_cross'] = cross_contribution
                reasons.append("均线金叉")
            elif signals.get('death_cross'):
                cross_contribution = -15
                signal_strength += cross_contribution
                strength_details['death_cross'] = cross_contribution
                reasons.append("均线死叉")
            
            # MACD信号
            if signals.get('macd_golden_cross'):
                macd_contribution = 15
                signal_strength += macd_contribution
                strength_details['macd_golden_cross'] = macd_contribution
                reasons.append("MACD金叉")
            elif signals.get('macd_death_cross'):
                macd_contribution = -15
                signal_strength += macd_contribution
                strength_details['macd_death_cross'] = macd_contribution
                reasons.append("MACD死叉")
            
            if signals.get('macd_divergence') == 1:
                div_contribution = 10
                signal_strength += div_contribution
                strength_details['macd_bullish_divergence'] = div_contribution
                reasons.append("MACD底背离")
            elif signals.get('macd_divergence') == -1:
                div_contribution = -10
                signal_strength += div_contribution
                strength_details['macd_bearish_divergence'] = div_contribution
                reasons.append("MACD顶背离")
            
            # RSI信号
            if signals.get('rsi_oversold'):
                rsi_contribution = 10
                signal_strength += rsi_contribution
                strength_details['rsi_oversold'] = rsi_contribution
                reasons.append("RSI超卖")
            elif signals.get('rsi_overbought'):
                rsi_contribution = -10
                signal_strength += rsi_contribution
                strength_details['rsi_overbought'] = rsi_contribution
                reasons.append("RSI超买")
            
            # KDJ信号
            if signals.get('kdj_golden_cross'):
                kdj_contribution = 10
                signal_strength += kdj_contribution
                strength_details['kdj_golden_cross'] = kdj_contribution
                reasons.append("KDJ金叉")
            elif signals.get('kdj_death_cross'):
                kdj_contribution = -10
                signal_strength += kdj_contribution
                strength_details['kdj_death_cross'] = kdj_contribution
                reasons.append("KDJ死叉")
            
            # 成交量信号
            if signals.get('volume_price_positive'):
                vol_contribution = 10
                signal_strength += vol_contribution
                strength_details['volume_price_positive'] = vol_contribution
                reasons.append("放量上涨")
            elif signals.get('volume_price_negative'):
                vol_contribution = -10
                signal_strength += vol_contribution
                strength_details['volume_price_negative'] = vol_contribution
                reasons.append("放量下跌")
            
            # 支撑阻力信号
            if signals.get('near_support'):
                support_contribution = 5
                signal_strength += support_contribution
                strength_details['near_support'] = support_contribution
                reasons.append("接近支撑位")
            elif signals.get('near_resistance'):
                resistance_contribution = -5
                signal_strength += resistance_contribution
                strength_details['near_resistance'] = resistance_contribution
                reasons.append("接近阻力位")
            
            # 布林带信号
            if signals.get('bb_lower_breakout'):
                bb_contribution = 8
                signal_strength += bb_contribution
                strength_details['bb_lower_breakout'] = bb_contribution
                reasons.append("布林带下轨突破")
            elif signals.get('bb_upper_breakout'):
                bb_contribution = -8
                signal_strength += bb_contribution
                strength_details['bb_upper_breakout'] = bb_contribution
                reasons.append("布林带上轨突破")
            
            if signals.get('bb_squeeze'):
                bb_squeeze_contribution = 3
                signal_strength += bb_squeeze_contribution
                strength_details['bb_squeeze'] = bb_squeeze_contribution
                reasons.append("布林带收缩")
            
            # ATR信号
            if signals.get('atr_low_volatility'):
                atr_contribution = 3
                signal_strength += atr_contribution
                strength_details['atr_low_volatility'] = atr_contribution
                reasons.append("低波动率")
            elif signals.get('atr_high_volatility'):
                atr_contribution = -3
                signal_strength += atr_contribution
                strength_details['atr_high_volatility'] = atr_contribution
                reasons.append("高波动率")
            
            # CCI信号
            if signals.get('cci_oversold'):
                cci_contribution = 8
                signal_strength += cci_contribution
                strength_details['cci_oversold'] = cci_contribution
                reasons.append("CCI超卖")
            elif signals.get('cci_overbought'):
                cci_contribution = -8
                signal_strength += cci_contribution
                strength_details['cci_overbought'] = cci_contribution
                reasons.append("CCI超买")
            
            if signals.get('cci_bullish_divergence'):
                cci_div_contribution = 10
                signal_strength += cci_div_contribution
                strength_details['cci_bullish_divergence'] = cci_div_contribution
                reasons.append("CCI底背离")
            elif signals.get('cci_bearish_divergence'):
                cci_div_contribution = -10
                signal_strength += cci_div_contribution
                strength_details['cci_bearish_divergence'] = cci_div_contribution
                reasons.append("CCI顶背离")
            
            # 威廉指标信号
            if signals.get('williams_oversold'):
                williams_contribution = 8
                signal_strength += williams_contribution
                strength_details['williams_oversold'] = williams_contribution
                reasons.append("威廉指标超卖")
            elif signals.get('williams_overbought'):
                williams_contribution = -8
                signal_strength += williams_contribution
                strength_details['williams_overbought'] = williams_contribution
                reasons.append("威廉指标超买")
            
            # 随机指标信号
            if signals.get('stoch_oversold'):
                stoch_contribution = 8
                signal_strength += stoch_contribution
                strength_details['stoch_oversold'] = stoch_contribution
                reasons.append("随机指标超卖")
            elif signals.get('stoch_overbought'):
                stoch_contribution = -8
                signal_strength += stoch_contribution
                strength_details['stoch_overbought'] = stoch_contribution
                reasons.append("随机指标超买")
            
            if signals.get('stoch_golden_cross'):
                stoch_cross_contribution = 8
                signal_strength += stoch_cross_contribution
                strength_details['stoch_golden_cross'] = stoch_cross_contribution
                reasons.append("随机指标金叉")
            elif signals.get('stoch_death_cross'):
                stoch_cross_contribution = -8
                signal_strength += stoch_cross_contribution
                strength_details['stoch_death_cross'] = stoch_cross_contribution
                reasons.append("随机指标死叉")
            
            # ADX信号
            if signals.get('adx_strong_trend') and signals.get('adx_bullish'):
                adx_contribution = 10
                signal_strength += adx_contribution
                strength_details['adx_strong_bullish'] = adx_contribution
                reasons.append("ADX强势多头")
            elif signals.get('adx_strong_trend') and signals.get('adx_bearish'):
                adx_contribution = -10
                signal_strength += adx_contribution
                strength_details['adx_strong_bearish'] = adx_contribution
                reasons.append("ADX强势空头")
            
            # 动量信号
            if signals.get('price_near_low'):
                momentum_contribution = 5
                signal_strength += momentum_contribution
                strength_details['price_near_low'] = momentum_contribution
                reasons.append("价格接近低点")
            elif signals.get('price_near_high'):
                momentum_contribution = -5
                signal_strength += momentum_contribution
                strength_details['price_near_high'] = momentum_contribution
                reasons.append("价格接近高点")
            
            # 成交量趋势信号
            if signals.get('volume_trend_increasing'):
                vol_trend_contribution = 3
                signal_strength += vol_trend_contribution
                strength_details['volume_trend_increasing'] = vol_trend_contribution
                reasons.append("成交量趋势上升")
            elif signals.get('volume_trend_decreasing'):
                vol_trend_contribution = -3
                signal_strength += vol_trend_contribution
                strength_details['volume_trend_decreasing'] = vol_trend_contribution
                reasons.append("成交量趋势下降")
            
            # 计算信号强度（0-100）
            # 将信号强度标准化到0-100范围
            normalized_strength = min(max(abs(signal_strength), 0), 100)
            
            # 确定信号类型
            if signal_strength >= 30:
                signal_type = SignalType.STRONG_BUY
            elif signal_strength >= 15:
                signal_type = SignalType.BUY
            elif signal_strength <= -30:
                signal_type = SignalType.STRONG_SELL
            elif signal_strength <= -15:
                signal_type = SignalType.SELL
            else:
                signal_type = SignalType.HOLD
            
            # 计算置信度
            confidence = min(abs(signal_strength) / 50.0, 1.0)
            
            # 计算风险等级
            risk_level = self._calculate_risk_level(df, signals)
            
            # 计算止损止盈
            stop_loss, take_profit = self._calculate_stop_loss_take_profit(
                current_price, signals, signal_type
            )
            
            # 创建交易信号
            if signal_type != SignalType.HOLD:
                trading_signal = TradingSignal(
                    date=current_date,
                    signal_type=signal_type,
                    confidence=confidence,
                    strength=normalized_strength,
                    price=current_price,
                    volume=current_volume,
                    indicators=signals,
                    risk_level=risk_level,
                    reason=", ".join(reasons),
                    stop_loss=stop_loss,
                    take_profit=take_profit
                )
                # 记录交易信号到胜率跟踪器
                self.win_rate_tracker.record_signal(trading_signal)
                trading_signals.append(trading_signal)
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
        
        return trading_signals
    
    def _adjust_dynamic_weights(self, df: pd.DataFrame, signals: Dict[str, Any]):
        """根据市场状况动态调整指标权重"""
        try:
            # 计算市场波动性
            returns = df['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # 年化波动率

            # 评估趋势强度
            trend_strength = 0
            if signals.get('ma_bullish'):
                trend_strength = 1
            elif signals.get('ma_bullish') is False:
                trend_strength = -1

            # 评估成交量活跃度
            volume_mean = df['volume'].mean()
            current_volume = df.iloc[-1]['volume']
            volume_activity = min(current_volume / volume_mean, 5.0) if volume_mean > 0 else 1.0

            # 调整趋势指标权重
            if trend_strength == 1 and volatility < 0.3:
                self.dynamic_weights['trend'] = 1.2  # 强趋势低波动时增加趋势权重
            elif trend_strength == -1 and volatility < 0.3:
                self.dynamic_weights['trend'] = 1.2  # 强下跌趋势低波动时增加趋势权重
            else:
                self.dynamic_weights['trend'] = 1.0

            # 调整动量指标权重
            if volatility > 0.5:
                self.dynamic_weights['momentum'] = 0.8  # 高波动时降低动量权重
            else:
                self.dynamic_weights['momentum'] = 1.0

            # 调整成交量指标权重
            if volume_activity > 2.0:
                self.dynamic_weights['volume'] = 1.3  # 高成交量时增加成交量权重
            else:
                self.dynamic_weights['volume'] = 1.0

            # 调整波动率指标权重
            if volatility > 0.4:
                self.dynamic_weights['volatility'] = 1.2  # 高波动时增加波动率权重
            else:
                self.dynamic_weights['volatility'] = 1.0

            logger.debug(f"动态权重调整: {self.dynamic_weights}")

        except Exception as e:
            logger.error(f"动态权重调整失败: {e}")

    def _calculate_technical_score(self, signals: Dict[str, Any]) -> float:
        """计算技术面评分"""
        try:
            score = 50.0  # 基础分
            
            # 获取胜率数据
            win_rate = 0.5  # 默认值
            if hasattr(self, 'win_rate_tracker'):
                # 尝试获取相关胜率数据，可以根据股票代码和信号类型进行更精确的查询
                # 这里简化处理，获取整体胜率
                win_rate = self.win_rate_tracker.get_overall_win_rate() or 0.5
            
            # 趋势评分 (使用动态权重)
            if signals.get('ma_bullish'):
                score += 20 * self.dynamic_weights['trend']
            elif signals.get('ma_bullish') is False:
                score -= 20 * self.dynamic_weights['trend']
            
            if signals.get('golden_cross'):
                score += 15 * self.dynamic_weights['trend']
            elif signals.get('death_cross'):
                score -= 15 * self.dynamic_weights['trend']
            
            # 动量评分 (使用动态权重)
            if signals.get('macd_signal') == 1:
                score += 15 * self.dynamic_weights['momentum']
            elif signals.get('macd_signal') == -1:
                score -= 15 * self.dynamic_weights['momentum']
            
            if signals.get('rsi_signal') == 1:
                score += 10 * self.dynamic_weights['momentum']
            elif signals.get('rsi_signal') == -1:
                score -= 10 * self.dynamic_weights['momentum']
            
            # 成交量评分 (使用动态权重)
            if signals.get('volume_price_positive'):
                score += 10 * self.dynamic_weights['volume']
            elif signals.get('volume_price_negative'):
                score -= 10 * self.dynamic_weights['volume']
            
            # 波动率评分 (使用动态权重)
            if signals.get('atr_high_volatility'):
                score -= 8 * self.dynamic_weights['volatility']
            elif signals.get('atr_low_volatility'):
                score += 5 * self.dynamic_weights['volatility']
            
            # 胜率评分 - 新增部分
            # 将胜率转换为-10到+10的贡献值
            win_rate_contribution = (win_rate - 0.5) * 20
            score += win_rate_contribution
            
            # 确保分数在0-100范围内
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"计算技术评分失败: {e}")
            return 50.0

    def update_trading_results(self, code: Optional[str] = None):
        """更新交易信号结果并计算胜率"""
        try:
            logger.info("开始更新交易信号结果")
            # 获取所有未平仓的交易信号
            open_signals = self.win_rate_tracker.get_open_signals(code)
            if not open_signals:
                logger.info("没有未平仓的交易信号")
                return

            for signal in open_signals:
                # 获取当前价格
                current_price = self._get_current_price(signal.code)
                if not current_price:
                    logger.warning(f"无法获取股票 {signal.code} 的当前价格")
                    continue

                # 检查是否达到止盈或止损条件
                is_win, is_closed = self._check_signal_outcome(signal, current_price)
                if is_closed:
                    # 更新信号结果
                    self.win_rate_tracker.update_signal_result(signal.stock_code, signal.signal_date, datetime.now().strftime('%Y%m%d'), current_price)
                    logger.info(f"更新信号结果: 股票 {signal.stock_code}, 信号类型 {signal.signal_type.name}, 结果: {'盈利' if is_win else '亏损'}")

            # 生成优化建议
            optimization_suggestions = self.win_rate_tracker.get_win_rate_enhancement_suggestions()
            if optimization_suggestions:
                logger.info("交易信号优化建议:")
                for suggestion in optimization_suggestions:
                    logger.info(f"- {suggestion}")

        except Exception as e:
            logger.error(f"更新交易信号结果失败: {e}")

    def _get_current_price(self, code: str) -> Optional[float]:
        """获取股票当前价格"""
        try:
            with db_manager.get_session() as session:
                stock = session.query(Stock).filter(Stock.code == code).first()
                if stock and stock.current_price:
                    return stock.current_price
            return None
        except Exception as e:
            logger.error(f"获取股票 {code} 当前价格失败: {e}")
            return None

    def _check_signal_outcome(self, signal: TradingSignal, current_price: float) -> Tuple[bool, bool]:
        """检查信号结果是否达到止盈或止损条件"""
        is_win = False
        is_closed = False

        if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
            # 买入信号
            if current_price >= signal.take_profit:
                is_win = True
                is_closed = True
            elif current_price <= signal.stop_loss:
                is_win = False
                is_closed = True
        elif signal.signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
            # 卖出信号
            if current_price <= signal.take_profit:
                is_win = True
                is_closed = True
            elif current_price >= signal.stop_loss:
                is_win = False
                is_closed = True

        return is_win, is_closed

    def _batch_save_results(self, results: list):
        """批量保存结果到数据库"""
        try:
            if not results:
                return

            logger.info(f"开始批量保存 {len(results)} 条记录到数据库")

                # 准备批量插入数据
            stock_records = []
            for result in results:
                stock_record = {
                    'code': result['code'],
                    'score': result['comprehensive_score'],
                    'score1': result['fundamental_score'],
                    'score2': result['technical_score'],
                    'score3': result['liquidity_score'],
                    'score4': result['sentiment_score'],
                    'lastscore': 0,
                    # 其他字段使用默认值
                    'limit': result['old_analysis'].limit,
                    'isBottomInversion': result['old_analysis'].isBottomInversion,
                    'isHeavyVolume': result['old_analysis'].isHeavyVolume,
                    'isTup': result['old_analysis'].isTup,
                    'ret': result['old_analysis'].ret,
                    'md': result['old_analysis'].md,
                    'alpha': result['old_analysis'].alpha,
                    'beta': result['old_analysis'].beta,
                    'momentum': result['old_analysis'].momentum,
                    'decisionPercent': result['old_analysis'].decisionPercent,
                    'ret10': result['old_analysis'].ret10,
                    'ret20': result['old_analysis'].ret20,
                    'ret100': result['old_analysis'].ret100,
                    'kline': result['old_analysis'].kline,
                    'emv': result['old_analysis'].emv,
                    'macd': result['old_analysis'].macd,
                    'ma': result['old_analysis'].ma,
                    'max30': result['old_analysis'].max30,
                }
                stock_records.append(stock_record)

            # 批量插入/更新
            with db_manager.get_session() as session:
                for record in stock_records:
                    insert_stmt = insert(Stock).values(**record)
                    on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**record)
                    session.execute(on_duplicate_key_stmt)

                session.commit()

            logger.info("批量保存完成")

        except Exception as e:
            logger.error(f"批量保存到数据库失败: {e}")
    
    def _calculate_risk_score(self, df: pd.DataFrame, signals: Dict[str, Any]) -> float:
        """计算风险评分"""
        try:
            risk_score = 50.0  # 基础风险分
            
            # 波动率风险
            returns = df['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            
            if volatility > 0.5:  # 高波动
                risk_score += 30
            elif volatility > 0.3:  # 中等波动
                risk_score += 15
            
            # 价格位置风险
            if signals.get('rsi_overbought'):
                risk_score += 20
            elif signals.get('rsi_oversold'):
                risk_score -= 10
            
            # 成交量风险
            if signals.get('volume_surge') and signals.get('volume_price_negative'):
                risk_score += 15
            
            # 确保分数在0-100范围内
            return max(0, min(100, risk_score))
            
        except Exception as e:
            logger.error(f"计算风险评分失败: {e}")
            return 50.0
    
    def _calculate_risk_level(self, df: pd.DataFrame, signals: Dict[str, Any]) -> RiskLevel:
        """计算风险等级"""
        try:
            risk_score = self._calculate_risk_score(df, signals)
            
            if risk_score >= 80:
                return RiskLevel.VERY_HIGH
            elif risk_score >= 60:
                return RiskLevel.HIGH
            elif risk_score >= 40:
                return RiskLevel.MEDIUM
            else:
                return RiskLevel.LOW
                
        except Exception as e:
            logger.error(f"计算风险等级失败: {e}")
            return RiskLevel.MEDIUM
    
    def _calculate_stop_loss_take_profit(self, current_price: float, 
                                       signals: Dict[str, Any], 
                                       signal_type: SignalType) -> Tuple[Optional[float], Optional[float]]:
        """计算止损止盈位"""
        try:
            if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                # 买入信号：止损位在支撑位下方，止盈位在阻力位
                stop_loss = signals.get('nearest_support')
                if stop_loss:
                    stop_loss = stop_loss * 0.95  # 支撑位下方5%
                
                take_profit = signals.get('nearest_resistance')
                if take_profit:
                    take_profit = take_profit * 1.05  # 阻力位上方5%
                
                return stop_loss, take_profit
                
            elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                # 卖出信号：止损位在阻力位上方，止盈位在支撑位
                stop_loss = signals.get('nearest_resistance')
                if stop_loss:
                    stop_loss = stop_loss * 1.05  # 阻力位上方5%
                
                take_profit = signals.get('nearest_support')
                if take_profit:
                    take_profit = take_profit * 0.95  # 支撑位下方5%
                
                return stop_loss, take_profit
            
            return None, None
            
        except Exception as e:
            logger.error(f"计算止损止盈失败: {e}")
            return None, None
    
    def analyze_signal_strength(self, signal: TradingSignal) -> Dict[str, Any]:
        """分析信号强度构成"""
        try:
            strength_analysis = {
                'total_strength': signal.strength,
                'confidence': signal.confidence,
                'signal_type': signal.signal_type.value,
                'risk_level': signal.risk_level.value,
                'contributions': {}
            }
            
            # 分析各指标对信号强度的贡献
            indicators = signal.indicators
            
            # 均线贡献
            if indicators.get('ma_bullish'):
                strength_analysis['contributions']['均线多头排列'] = 20
            elif indicators.get('ma_bullish') is False:
                strength_analysis['contributions']['均线空头排列'] = -20
            
            if indicators.get('golden_cross'):
                strength_analysis['contributions']['均线金叉'] = 15
            elif indicators.get('death_cross'):
                strength_analysis['contributions']['均线死叉'] = -15
            
            # MACD贡献
            if indicators.get('macd_golden_cross'):
                strength_analysis['contributions']['MACD金叉'] = 15
            elif indicators.get('macd_death_cross'):
                strength_analysis['contributions']['MACD死叉'] = -15
            
            if indicators.get('macd_divergence') == 1:
                strength_analysis['contributions']['MACD底背离'] = 10
            elif indicators.get('macd_divergence') == -1:
                strength_analysis['contributions']['MACD顶背离'] = -10
            
            # RSI贡献
            if indicators.get('rsi_oversold'):
                strength_analysis['contributions']['RSI超卖'] = 10
            elif indicators.get('rsi_overbought'):
                strength_analysis['contributions']['RSI超买'] = -10
            
            # KDJ贡献
            if indicators.get('kdj_golden_cross'):
                strength_analysis['contributions']['KDJ金叉'] = 10
            elif indicators.get('kdj_death_cross'):
                strength_analysis['contributions']['KDJ死叉'] = -10
            
            # 成交量贡献
            if indicators.get('volume_price_positive'):
                strength_analysis['contributions']['放量上涨'] = 10
            elif indicators.get('volume_price_negative'):
                strength_analysis['contributions']['放量下跌'] = -10
            
            # 支撑阻力贡献
            if indicators.get('near_support'):
                strength_analysis['contributions']['接近支撑位'] = 5
            elif indicators.get('near_resistance'):
                strength_analysis['contributions']['接近阻力位'] = -5
            
            # 布林带贡献
            if indicators.get('bb_lower_breakout'):
                strength_analysis['contributions']['布林带下轨突破'] = 8
            elif indicators.get('bb_upper_breakout'):
                strength_analysis['contributions']['布林带上轨突破'] = -8
            
            if indicators.get('bb_squeeze'):
                strength_analysis['contributions']['布林带收缩'] = 3
            
            # CCI贡献
            if indicators.get('cci_oversold'):
                strength_analysis['contributions']['CCI超卖'] = 8
            elif indicators.get('cci_overbought'):
                strength_analysis['contributions']['CCI超买'] = -8
            
            if indicators.get('cci_bullish_divergence'):
                strength_analysis['contributions']['CCI底背离'] = 10
            elif indicators.get('cci_bearish_divergence'):
                strength_analysis['contributions']['CCI顶背离'] = -10
            
            # 威廉指标贡献
            if indicators.get('williams_oversold'):
                strength_analysis['contributions']['威廉指标超卖'] = 8
            elif indicators.get('williams_overbought'):
                strength_analysis['contributions']['威廉指标超买'] = -8
            
            # 随机指标贡献
            if indicators.get('stoch_oversold'):
                strength_analysis['contributions']['随机指标超卖'] = 8
            elif indicators.get('stoch_overbought'):
                strength_analysis['contributions']['随机指标超买'] = -8
            
            if indicators.get('stoch_golden_cross'):
                strength_analysis['contributions']['随机指标金叉'] = 8
            elif indicators.get('stoch_death_cross'):
                strength_analysis['contributions']['随机指标死叉'] = -8
            
            # ADX贡献
            if indicators.get('adx_strong_trend') and indicators.get('adx_bullish'):
                strength_analysis['contributions']['ADX强势多头'] = 10
            elif indicators.get('adx_strong_trend') and indicators.get('adx_bearish'):
                strength_analysis['contributions']['ADX强势空头'] = -10
            
            # 动量贡献
            if indicators.get('price_near_low'):
                strength_analysis['contributions']['价格接近低点'] = 5
            elif indicators.get('price_near_high'):
                strength_analysis['contributions']['价格接近高点'] = -5
            
            # 成交量趋势贡献
            if indicators.get('volume_trend_increasing'):
                strength_analysis['contributions']['成交量趋势上升'] = 3
            elif indicators.get('volume_trend_decreasing'):
                strength_analysis['contributions']['成交量趋势下降'] = -3
            
            return strength_analysis
            
        except Exception as e:
            logger.error(f"分析信号强度失败: {e}")
            return {}

def print_analysis_result(analysis: StockAnalysis, market_context=None):
    """打印分析结果，支持行情参考"""
    print(f"\n{'='*80}")
    print(f"📊 股票分析报告: {analysis.code} ({analysis.name})")
    print(f"{'='*80}")
    print(f"📅 分析日期: {analysis.last_update}")
    print(f"💰 当前价格: ¥{analysis.current_price:.2f}")
    print(f"📈 技术面评分: {analysis.technical_score:.1f}/100")
    print(f"📊 基本面评分: {analysis.fundamental_score:.1f}/100")
    print(f"📰 风口面评分: {analysis.sentiment_score:.1f}/100")
    print(f"💰 资金面评分: {analysis.liquidity_score:.1f}/100")
    print(f"🎯 综合评分: {analysis.comprehensive_score:.1f}/100")
    print(f"💡 投资建议: {analysis.recommendation}")
    if analysis.signals:
        print(f"\n🎯 交易信号:")
        for i, signal in enumerate(analysis.signals, 1):
            print(f"  {i}. {signal.signal_type.value} (置信度: {signal.confidence:.1%}, 强度: {signal.strength:.1f}/100)")
            print(f"     价格: ¥{signal.price:.2f} | 成交量: {signal.volume:,.0f}")
            print(f"     原因: {signal.reason}")
            if signal.stop_loss:
                print(f"     止损: ¥{signal.stop_loss:.2f}")
            if signal.take_profit:
                print(f"     止盈: ¥{signal.take_profit:.2f}")
            print(f"     风险等级: {signal.risk_level.value}")
            # 显示技术指标详情（略）
    else:
        print(f"\n📊 暂无明确交易信号")
    if market_context:
        print("----------------------------------------")
        print("【行情参考】")
        if market_context.get('macro'):
            print(f"宏观面评分: {market_context['macro'].overall_score:.2f}/100")
        if market_context.get('risk'):
            print(f"风险面评分: {market_context['risk'].overall_score:.2f}/100")
        if market_context.get('behavioral'):
            print(f"行为面评分: {market_context['behavioral'].overall_score:.2f}/100")
        if market_context.get('market'):
            print(f"市场面评分: {market_context['market'].overall_score:.2f}/100")
    print(f"{'='*80}")

def print_market_scan_results(results: List[StockAnalysis], market_context=None, limit: int = 20):
    """打印市场扫描结果，支持行情参考"""
    # 只保留最强信号的股票
    filtered_results = []
    for analysis in results:
        if not analysis.signals:
            continue
        last_signal = analysis.signals[-1]
        # 存入数据库
        with db_manager.get_session() as session:
            new_stock = Stock(
                code=analysis.code,
                desc2=last_signal.reason,
            )
            session.merge(new_stock)
            session.commit()
        if (
            last_signal.signal_type in [SignalType.STRONG_BUY, SignalType.STRONG_SELL]
            and last_signal.strength >= 50
            and last_signal.confidence >= 0.65
            and analysis.comprehensive_score >= 60
        ):
            filtered_results.append(analysis)
    print(f"\n{'='*140}")
    print(f"🔍 市场信号扫描结果 (显示前{limit}只)")
    print(f"{'='*160}")
    print(f"{'代码':<8} {'名称':<12} {'价格':<8} {'技术':<6} {'基本':<6} {'风口':<6} {'资金':<6} {'综合':<6} {'信号强度':<8} {'信号类型':<12} {'原因'} {'建议'}")
    print(f"{'-'*160}")
    for i, analysis in enumerate(filtered_results[:limit], 1):
        signal_type = analysis.signals[-1].signal_type.value if analysis.signals else "HOLD"
        signal_strength = analysis.signals[-1].strength if analysis.signals else 0
        recommendation = analysis.recommendation.split(' - ')[0] if analysis.recommendation else "观望"
        reason = analysis.signals[-1].reason if analysis.signals else ""
        print(f"{analysis.code:<8} {analysis.name:<12} "
              f"¥{analysis.current_price:<7.2f} {analysis.technical_score:<6.1f} "
              f"{analysis.fundamental_score:<6.1f} {analysis.sentiment_score:<6.1f} "
              f"{analysis.liquidity_score:<6.1f} {analysis.comprehensive_score:<6.1f} "
              f"{signal_strength:<8.1f} {signal_type:<12} {reason} {recommendation}")
    if market_context:
        print("----------------------------------------")
        print("【行情参考】")
        if market_context.get('macro'):
            print(f"宏观面评分: {market_context['macro'].overall_score:.2f}/100")
        if market_context.get('risk'):
            print(f"风险面评分: {market_context['risk'].overall_score:.2f}/100")
        if market_context.get('behavioral'):
            print(f"行为面评分: {market_context['behavioral'].overall_score:.2f}/100")
        if market_context.get('market'):
            print(f"市场面评分: {market_context['market'].overall_score:.2f}/100")
    print(f"{'='*160}")


# 其它功能补充
def extra_function(date: str): # 其它功能补充
    """
    其它功能补充
    """
    logger.info(f"完成股票分析，共处理 {len(quant_stock_list)} 只股票")

    # 验证数据完整性
    logger.info(f"收集到 {len(quant_stock_list)} 个分析结果")

    # 验证数据结构
    valid_results = []
    for i, result in enumerate(quant_stock_list):
        if result is None:
            logger.warning(f"跳过第 {i} 个空结果")
            continue
        # 增加容错性，接受不同类型的结果
        if isinstance(result, list):
            # 旧格式：列表
            if len(result) >= 1:
                valid_results.append(result)
            else:
                logger.warning(f"跳过第 {i} 个空列表结果")
        elif hasattr(result, '__dict__'):
            # 新格式：对象
            # 提取所需字段
            try:
                code = result.code
                isBottomInversion = getattr(result, 'isBottomInversion', False)
                isHeavyVolume = getattr(result, 'isHeavyVolume', False)
                isTup = getattr(result, 'isTup', False)
                macd = getattr(result, 'macd', 0)
                ma = getattr(result, 'ma', False)
                emv = getattr(result, 'emv', False)
                strongTrend = getattr(result, 'strongTrend', False)
                breakoutPlatform = getattr(result, 'breakoutPlatform', False)
                bigDivergence = getattr(result, 'bigDivergence', False)
                fundAccumulation = getattr(result, 'fundAccumulation', False)
                buySignal = getattr(result, 'buySignal', False)

                valid_result = [
                    code, isBottomInversion, isHeavyVolume, isTup, macd, ma, emv,
                    strongTrend, breakoutPlatform, bigDivergence, fundAccumulation, buySignal
                ]
                valid_results.append(valid_result)
            except Exception as e:
                logger.warning(f"转换第 {i} 个对象结果失败: {e}")
        else:
            logger.warning(f"跳过第 {i} 个未知类型的结果: {type(result)}")
            # 尝试转换为字符串并记录，帮助调试
            try:
                logger.debug(f"未知结果内容: {str(result)[:100]}")
            except:
                pass

    if not valid_results:
        logger.error("没有找到格式正确的分析结果")
        # 尝试从原始列表中提取可能的有效代码
        possible_codes = []
        for result in quant_stock_list:
            try:
                if isinstance(result, list) and len(result) > 0 and isinstance(result[0], str):
                    possible_codes.append(result[0])
                elif hasattr(result, 'code'):
                    possible_codes.append(result.code)
            except:
                pass
        if possible_codes:
            logger.warning(f"但找到了 {len(possible_codes)} 个可能的股票代码: {possible_codes[:10]}{'...' if len(possible_codes) > 10 else ''}")

    logger.info(f"验证通过，有效结果数: {len(valid_results)}")

    # 量化选股结果整理
    df_quant = pd.DataFrame(
        data=valid_results,
        columns=['code', 'isBottomInversion', 'isHeavyVolume', 'isTup', 'macd', 'ma', 'emv',
                'strongTrend', 'breakoutPlatform', 'bigDivergence', 'fundAccumulation', 'buySignal']
    )

    # 各种策略筛选和保存
    strategies = [
        # 优化后的原有策略
        ('[量化]底部反转', '持续下跌后的放量大阳，均线上穿', 'isBottomInversion', True),
        ('[量化]成交量是前5天的两倍', '放量实体阳线', 'isHeavyVolume', True),
        ('[量化]三连阳', '连续三日上涨模式', 'isTup', True),
        ('[量化]底背离', 'MACD底背离信号', 'macd', -1),
        ('[量化]5日线上穿10日线和30日线', '均线多头排列', 'ma', True),
        ('[量化]EMV信号', 'EMV技术指标信号', 'emv', True),
        # 新增策略
        ('[量化]强势趋势股', '沿着5日线上涨，强势特征明显', 'strongTrend', True),
        ('[量化]脱离平台', '成交量小碎步放大，行情刚起步', 'breakoutPlatform', True),
        ('[量化]大分歧', '两天长上下影，市场分歧转折点', 'bigDivergence', True),
        ('[量化]资金吸筹', '主力资金持续吸筹，量价配合良好', 'fundAccumulation', True),
        ('[量化]购买信号', '购买信号', 'buySignal', True)
    ]

    for name, desc, column, value in strategies:
        try:
            if column in df_quant.columns:
                selected_stocks = df_quant[df_quant[column] == value]['code'].tolist()
                save_sql(name, desc, selected_stocks)
            else:
                logger.warning(f"列 {column} 不存在，跳过策略 {name}")
        except Exception as e:
            logger.error(f"处理策略 {name} 失败: {e}")

    logger.info("功能1完成: 个股量化分析和策略筛选")


    """
    功能2: 标星个股计算和趋势股票筛选

    :return: 是否成功
    """
    try:
        logger.info("功能2: 标星个股计算")

        # 使用与市场扫描模式一致的日期生成方式
        today = get_current_date()

        with db_manager.get_session() as session:
            # 清除之前的标星状态
            session.query(Stock).filter(Stock.quant == 1).update({
                "quant": False,
                "star": False
            })
            session.commit()

            # 构建查询SQL
            sql = '''
                SELECT a.code, a.decisionPercent, a.kline, a.momentum,
                       b.f20, b.f6, b.displayName
                FROM stock a
                LEFT JOIN source b ON b.code = a.code
                WHERE b.f20 > {value} AND b.f6 > {amount}
                  AND a.momentum = true
                ORDER BY a.decisionPercent DESC
                LIMIT 1000
            '''.format(value=quantRaw.averageValue, amount=quantRaw.averageAmount)

            # 执行查询
            result = session.execute(sql)
            rows = result.fetchall()

            if not rows:
                logger.warning("没有找到符合条件的标星股票")
                return False

            logger.info(f"找到 {len(rows)} 只符合标星条件的股票")

            # 更新每日统计
            try:
                new_daily = Daliy(
                    date=date,
                    starcount=len(rows)
                )
                session.merge(new_daily)
                session.commit()
                logger.info(f"成功更新每日统计: {date}, 标星数量: {len(rows)}")
            except Exception as e:
                session.rollback()
                logger.error(f"更新每日统计失败: {str(e)}", exc_info=True)
                # 可以选择在这里返回False或者继续执行，根据业务需求决定
                # return False

            # 批量更新标星状态
            star_codes = []
            for row in rows:
                star_codes.append(row['code'])

                item = {
                    'code': row['code'],
                    'star': True,
                    'quant': True
                }
                insert_stmt = insert(Stock).values(**item)
                on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
                session.execute(on_duplicate_key_stmt)

            session.commit()

            logger.info(f"功能2完成: 标星 {len(star_codes)} 只股票")
            return True

    except Exception as e:
        logger.error(f"功能2执行失败: {e}")
        return False

@monitor_performance
def main():
    """主函数"""
    try:
        logger.info("🚀 启动股票买卖点分析工具")
        
        # 解析命令行参数
        parser = argparse.ArgumentParser(description="股票买卖点分析工具")
        parser.add_argument("-c", "--code", type=str, help="股票代码")
        parser.add_argument("-b", "--begin", type=str, help="开始日期 (YYYYMMDD)")
        parser.add_argument("-e", "--end", type=str, help="结束日期 (YYYYMMDD)")
        parser.add_argument("-d", "--date", type=str, help="分析日期 (YYYYMMDD)")
        parser.add_argument("--scan", action="store_true", help="扫描市场信号")
        parser.add_argument("--limit", type=int, default=20, help="显示结果数量限制")
        parser.add_argument("--optimized", action="store_true", help="使用优化模式（更快的计算）")
        parser.add_argument("--workers", type=int, default=8, help="并行处理的线程数")
        
        args = parser.parse_args()
        
        initialize_system()

        # 根据参数选择分析器配置
        analyzer = TradingSignalAnalyzer()
        if args.optimized:
            logger.info(f"使用优化模式，线程数: {args.workers}")
            analyzer.batch_processor = BatchProcessor(max_workers=args.workers)
        
        if args.scan:
            # 市场扫描模式
            date = get_current_date(args.date)
            print(f"🔍 开始市场扫描，目标日期: {date}")
            if args.optimized:
                print("⚡ 使用优化模式进行快速扫描...")
            results = analyzer.scan_market_signals(date, use_optimized=args.optimized)
            print_market_scan_results(results, analyzer.market_context, args.limit)
            # 更新交易结果
            analyzer.update_trading_results()
            extra_function(date)
        elif args.code:
            # 单股分析模式
            start_date = args.begin or ago_day_timestr(120, '%Y%m%d')
            end_date = args.end or args.date or time.strftime('%Y%m%d', time.localtime())
            # 单股分析时也可输出行情参考
            analyzer.analyze_market_context(end_date)
            analysis = analyzer.analyze_stock(args.code, start_date, end_date)
            if analysis:
                print_analysis_result(analysis, analyzer.market_context)
                # 更新该股票的交易结果
                analyzer.update_trading_results(args.code)
            else:
                print(f"❌ 分析股票 {args.code} 失败")
        else:
            print("请指定股票代码 (-c) 或使用市场扫描模式 (--scan)")
            parser.print_help()
        logger.info("✅ 股票买卖点分析工具执行完成")
        quant_monitor(27, True)
        return True
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        quant_monitor(27, False)
        return False
    except Exception as e:
        import traceback
        logger.error(f"股票买卖点分析工具执行失败: {e}")
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        quant_monitor(27, False)
        return False

if __name__ == '__main__':
    setup_signal_handler()
    
    try:
        success = main()
        cleanup_and_exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
        cleanup_and_exit(1)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        cleanup_and_exit(1)