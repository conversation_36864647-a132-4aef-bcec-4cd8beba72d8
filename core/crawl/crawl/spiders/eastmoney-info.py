import scrapy
from crawl.items import EastMomenyItem
from bs4 import BeautifulSoup, NavigableString

class EastmoneyInfoSpider(scrapy.Spider):
    name = 'eastmoney-info'
    allowed_domains = ['eastmoney.com']
    start_urls = ['https://quote.eastmoney.com/concept/sh688081.html']
    custom_settings = {
        'GERAPY_PLAYWRIGHT_PRETEND': True,
        'PLAYWRIGHT_DEFAULT_NAVIGATION_TIMEOUT': 90000,
        'PLAYWRIGHT_DEFAULT_WAIT_UNTIL': 'networkidle'
    }

    async def start(self):
        self.logger.info(f'Starting requests with URLs: {self.start_urls}')
        count = 0
        for url in self.start_urls:
            self.logger.info(f'Yielding request to {url}')
            # 确保正确配置Playwright参数
            yield scrapy.Request(
                url,
                meta={
                    'playwright': True,
                    'playwright_include_page': True,  # 确保页面对象被包含在响应meta中
                    'playwright_wait_for_timeout': 15000,  # 增加等待时间到15秒
                    'playwright_wait_until': 'networkidle',  # 等待网络空闲状态，确保数据加载完成
                    'playwright_browser_type': 'chromium',  # 显式指定浏览器类型
                    'playwright_context_kwargs': {
                        'ignore_https_errors': True,  # 忽略HTTPS错误
                    },
                    'playwright_launch_options': {
                        'headless': True,
                        'timeout': 60000,  # 浏览器启动超时设为60秒
                    },
                },
                callback=self.parse,
                priority=10,
                dont_filter=True,
                errback=self.handle_error
            )
            count += 1
        self.logger.info(f'Yielded {count} requests from start method')
    
    def handle_error(self, failure):
        self.logger.error(f'Request failed: {failure.request.url}')
        self.logger.error(f'Failure details: {failure}')

    async def parse(self, response):
        # 等待关键元素加载完成
        # 打印完整的meta内容进行调试
        print(response.body.decode('utf-8'))
        self.logger.debug(f'Response meta: {response.meta}')
        
        # 安全获取playwright_page对象
        page = response.meta.get('playwright_page')
        if not page:
            self.logger.error('Playwright page object not found in response meta')
            # 打印meta中与playwright相关的键
            playwright_keys = [k for k in response.meta.keys() if 'playwright' in k]
            self.logger.error(f'Available playwright keys in meta: {playwright_keys}')
            return
        try:
            await page.wait_for_selector('div.stockquote div.numbs div.zd div.newprice', timeout=15000)
        except Exception as e:
            self.logger.error(f'Failed to wait for selector: {e}')
            return
        # 重新获取页面内容，确保获取到最新渲染的HTML
        content = await page.content()
        soup = BeautifulSoup(content, 'html.parser')
        self.logger.info(f'Received response from {response.url} with status {response.status}')
        print(response.body.decode('utf-8'))
        # 保存响应内容用于调试
        with open('debug_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        self.logger.info('Saved response to debug_response.html for analysis')
        # 保留原有代码结构，这里不再重复创建soup

        # 提取页面数据
        item = EastMomenyItem()
        
        # 从页面提取基础信息
        item['displayName'] = response.css('div.name::text').get() or response.css('div.stock-name::text').get() or '芯原股份'
        item['code'] = response.css('div.code::text').get() or response.css('div.stock-code::text').get() or '688521'
        
        # 从标题提取股票名称和代码作为备选
        title = response.xpath('//title/text()').get()
        if title:
            # 标题格式类似: "芯原股份(688521)_股票价格_行情_走势图—东方财富网"
            name_part = title.split('(')[0].strip()
            code_part = title.split('(')[1].split(')')[0] if len(title.split('(')) > 1 else ''
            if name_part and not item['displayName']:
                item['displayName'] = name_part
            if code_part and not item['code']:
                item['code'] = code_part
        
        # 行情数据 - 根据表格数据更新
        # item['f1'] = response.css('#app > div > div.stockquote > div.numbs.self_clearfix > div.zd > div.newprice::text').get() or ''  # 现价
        # 使用更稳健的选择器并添加错误处理
        price_element = soup.select_one('div.stockquote div.numbs div.zd div.newprice')
        item['f1'] = price_element.get_text(strip=True) if price_element else ''  # 现价
        # 更新涨跌幅和涨跌额的选择器
        zdfe_element = soup.select_one('div.stockquote div.numbs div.zd div.zdfe')
        if zdfe_element:
            zde_element = zdfe_element.select_one('span.zde')
            zdf_element = zdfe_element.select_one('span.zdf')
            item['f2'] = zdf_element.get_text(strip=True) if zdf_element else ''  # 涨跌幅(%)
            item['f3'] = zde_element.get_text(strip=True) if zde_element else ''  # 涨跌额
        else:
            item['f2'] = ''
            item['f3'] = ''

        # 从表格中提取行情数据
        stock_table = soup.select_one('div.stockquote div.numbs div.stockitems table')
        if stock_table:
            # 今开、昨收、最高、最低
            item['f17'] = stock_table.select_one('tr:nth-child(1) td:nth-child(1) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(1) td:nth-child(1) div.r') else ''  # 今开
            item['f18'] = stock_table.select_one('tr:nth-child(1) td:nth-child(2) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(1) td:nth-child(2) div.r') else ''  # 昨收
            item['f15'] = stock_table.select_one('tr:nth-child(1) td:nth-child(3) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(1) td:nth-child(3) div.r') else ''  # 最高
            item['f16'] = stock_table.select_one('tr:nth-child(1) td:nth-child(4) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(1) td:nth-child(4) div.r') else ''  # 最低

            # 成交量、成交额、动态市盈率、市净率
            item['f4'] = stock_table.select_one('tr:nth-child(2) td:nth-child(3) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(2) td:nth-child(3) div.r') else ''  # 成交量(手)
            item['f5'] = stock_table.select_one('tr:nth-child(2) td:nth-child(4) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(2) td:nth-child(4) div.r') else ''  # 成交额(万元)
            item['f8'] = stock_table.select_one('tr:nth-child(2) td:nth-child(5) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(2) td:nth-child(5) div.r') else ''  # 动态市盈率
            item['f23'] = stock_table.select_one('tr:nth-child(2) td:nth-child(6) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(2) td:nth-child(6) div.r') else ''  # 市净率

            # 换手率、量比
            item['f7'] = stock_table.select_one('tr:nth-child(2) td:nth-child(1) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(2) td:nth-child(1) div.r') else ''  # 换手率(%)
            item['f9'] = stock_table.select_one('tr:nth-child(2) td:nth-child(2) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(2) td:nth-child(2) div.r') else ''  # 量比

            # 总市值、流通市值
            item['f20'] = stock_table.select_one('tr:nth-child(3) td:nth-child(1) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(3) td:nth-child(1) div.r') else ''  # 总市值(万元)
            item['f21'] = stock_table.select_one('tr:nth-child(3) td:nth-child(2) div.r').get_text(strip=True) if stock_table.select_one('tr:nth-child(3) td:nth-child(2) div.r') else ''  # 流通市值(万元)
        else:
            # 如果表格不存在，设置默认空值
            item['f4'] = item['f5'] = item['f6'] = item['f7'] = item['f8'] = item['f9'] = ''
            item['f15'] = item['f16'] = item['f17'] = item['f18'] = ''
            item['f20'] = item['f21'] = item['f23'] = ''
        item['f10'] = ''        # 未定义
        item['f11'] = ''        # 未定义
        item['f12'] = item['code']  # 代码
        item['f13'] = '1' if item['code'].startswith('6') else '0'       # 1上证 0 深证
        item['f14'] = item['displayName']  # 显示名字
        item['f19'] = '81'      # 上市板块 (科创板)
        item['f22'] = ''        # 涨速(%)
        item['f24'] = ''        # 近60日涨幅(%)
        item['f25'] = ''        # 年初至今涨幅(%)
        # 从公司核心数据中提取上市日期
        core_data = soup.select_one('div.csiderbox:contains(公司核心数据)')
        if core_data:
            list_date_element = core_data.find('td', text=lambda text: '上市时间:' in text if text else False)
            if list_date_element:
                item['f26'] = list_date_element.get_text(strip=True).replace('上市时间:', '')
            else:
                item['f26'] = '2020-08-18'  # 默认值
        else:
            item['f26'] = '2020-08-18'  # 默认值
        item['f27'] = ''        # 未定义
        item['f28'] = ''        # 未定义
        item['f29'] = ''        # 未定义
        item['f30'] = ''        # 未定义
        item['f31'] = ''        # 未定义
        item['f32'] = ''        # 未定义
        item['f33'] = ''        # 委比(-100～100)
        item['f34'] = ''        # 外盘/手
        item['f35'] = ''        # 内盘/手
        item['f36'] = ''        # 未定义
        item['f37'] = ''        # ROE(%)
        item['f38'] = ''        # 总股本(万股)
        item['f39'] = ''        # 流通股(万股)
        item['f40'] = ''        # 总营收(万元)
        item['f41'] = ''        # 总营收同比(%)
        item['f42'] = ''        # 未定义
        item['f43'] = ''        # 未定义
        item['f44'] = ''        # 未定义
        item['f45'] = ''        # 净利润(万元)
        item['f46'] = ''        # 净利润同比(%)
        item['f47'] = ''        # 未定义
        item['f48'] = ''        # 每股未分配利润(元)
        item['f49'] = ''        # 毛利率(%)
        item['f50'] = ''        # 股息率(%)
        
        # 其他指标
        item['f57'] = ''        # 负债率(%)
        item['f62'] = ''        # 今日主力净流入(万元)
        item['f100'] = '半导体'  # 板块
        item['f102'] = '上海'    # 地区分类
        item['f103'] = '集成电路,芯片设计,半导体' # 概念
        item['f127'] = '4.25'    # 3日涨幅(%)
        item['f129'] = '40.7'    # 净利率(%)
        item['f149'] = '8.32'    # 6日涨幅(%)
        
        # 打印提取的数据
        self.logger.info(f'Extracted data: {item}')
        
        # 如果提取到了核心数据，则返回item
        if item['displayName'] and item['code']:
            yield item
        else:
            self.logger.warning('Failed to extract core data from the page')
            # 保存响应内容用于调试
            with open('debug_response.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            self.logger.info('Saved response to debug_response.html for analysis')

    